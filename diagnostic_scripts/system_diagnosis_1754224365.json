{"timestamp": "2025-08-03T14:32:45.628894", "diagnosis_summary": {"total_issues": 0, "critical_issues": 0, "high_priority_issues": 0, "medium_priority_issues": 0, "overall_health": "GOOD", "unified_modules_found": 0, "consistency_issues": 0, "recommendations_count": 0}, "critical_issues": [], "high_priority_issues": [], "medium_priority_issues": [], "architecture_analysis": {"unified_modules_count": 0, "websocket_modules": [], "core_modules": [], "missing_modules": ["unified_timestamp_processor", "unified_connection_pool_manager", "unified_data_formatter", "unified_order_spread_calculator", "unified_balance_manager", "unified_depth_analyzer"], "duplicate_implementations": [], "interface_analysis": {"websocket_chain": {}, "timestamp_chain": {}, "api_chain": {}, "broken_chains": []}, "consistency_analysis": {"websocket_implementations": {"gate": {"websocket_exists": false, "exchange_exists": false, "timestamp_method": "unknown", "has_monitoring": false, "heartbeat_interval": "unknown"}, "bybit": {"websocket_exists": false, "exchange_exists": false, "timestamp_method": "unknown", "has_monitoring": false, "heartbeat_interval": "unknown"}, "okx": {"websocket_exists": false, "exchange_exists": false, "timestamp_method": "unknown", "has_monitoring": false, "heartbeat_interval": "unknown"}}, "timestamp_processing": {}, "api_handling": {}, "inconsistencies": []}, "duplication_analysis": {"duplicate_functions": [{"function": "calculate_data_age", "file": "./123/diagnostic_scripts/comprehensive_system_diagnosis.py", "type": "数据年龄计算重复实现"}, {"function": "calculate_data_age", "file": "./123/websocket/unified_timestamp_processor.py", "type": "数据年龄计算重复实现"}, {"function": "calculate_data_age", "file": "./123/core/data_snapshot_validator.py", "type": "数据年龄计算重复实现"}, {"function": "get_synced_timestamp", "files": ["./123/diagnostic_scripts/comprehensive_system_diagnosis.py", "./123/websocket/unified_timestamp_processor.py", "./123/core/data_snapshot_validator.py"], "type": "时间戳同步重复实现"}], "redundant_imports": [], "similar_implementations": [], "optimization_opportunities": []}}, "unified_modules_status": {"websocket_implementations": {}, "import_patterns": {}, "consistency_issues": []}, "websocket_health": {"okx_issues": [], "gate_issues": [], "bybit_status": [], "blocking_patterns": []}, "api_optimization_status": {"rate_limit_settings": {}, "optimization_issues": [], "recommendations": []}, "recommendations": []}