#!/usr/bin/env python3
"""
🎯 最终修复验证脚本
验证WebSocket并发冲突和时间戳一致性修复效果
"""

import asyncio
import json
import time
import sys
import os
from typing import Dict, List, Any
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.logger import get_logger

logger = get_logger("FinalFixVerification")

class FinalFixVerification:
    """最终修复验证器"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'verification_summary': {},
            'websocket_consistency': {},
            'timestamp_consistency': {},
            'architecture_compliance': {},
            'final_assessment': {}
        }
        
    async def run_verification(self):
        """运行最终验证"""
        logger.info("🎯 开始最终修复验证...")
        
        # 1. 验证WebSocket实现一致性
        await self.verify_websocket_consistency()
        
        # 2. 验证时间戳处理一致性
        await self.verify_timestamp_consistency()
        
        # 3. 验证架构合规性
        await self.verify_architecture_compliance()
        
        # 4. 生成最终评估
        await self.generate_final_assessment()
        
        return self.results
        
    async def verify_websocket_consistency(self):
        """验证WebSocket实现一致性"""
        logger.info("🔍 验证WebSocket实现一致性...")
        
        consistency_results = {
            'gate_ws': {'compliant': False, 'issues': []},
            'bybit_ws': {'compliant': False, 'issues': []},
            'okx_ws': {'compliant': False, 'issues': []}
        }
        
        try:
            exchanges = ['gate', 'bybit', 'okx']
            
            for exchange in exchanges:
                ws_file = f"websocket/{exchange}_ws.py"
                if os.path.exists(ws_file):
                    with open(ws_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    exchange_key = f"{exchange}_ws"
                    issues = []
                    
                    # 检查是否有并发冲突风险
                    if 'asyncio.gather(' in content and 'subscription_tasks' in content:
                        issues.append("存在asyncio.gather并发风险")
                    
                    # 检查是否有监控任务
                    if 'def _monitor_data_flow(' in content or 'async def _monitor_data_flow(' in content:
                        issues.append("存在_monitor_data_flow监控任务")
                    
                    # 🔥 精确检查：只检查实际的时间戳处理调用
                    # 检查是否所有时间戳调用都使用实例方法
                    if exchange in ['gate', 'bybit', 'okx']:
                        # 检查是否有非实例方法的时间戳调用
                        has_bad_calls = False
                        lines = content.split('\n')
                        for line in lines:
                            # 检查实际的时间戳处理调用（不是初始化）
                            if '.get_synced_timestamp(' in line and 'self.timestamp_processor.get_synced_timestamp(' not in line:
                                # 排除注释和初始化
                                if not line.strip().startswith('#') and 'get_timestamp_processor(' not in line:
                                    has_bad_calls = True
                                    break

                        if has_bad_calls:
                            issues.append("WebSocket模块存在非实例方法的时间戳调用")
                    
                    consistency_results[exchange_key]['issues'] = issues
                    consistency_results[exchange_key]['compliant'] = len(issues) == 0
                    
            self.results['websocket_consistency'] = consistency_results
            
        except Exception as e:
            logger.error(f"WebSocket一致性验证失败: {e}")
            
    async def verify_timestamp_consistency(self):
        """验证时间戳处理一致性"""
        logger.info("🔍 验证时间戳处理一致性...")
        
        timestamp_results = {
            'unified_usage': {},
            'consistency_score': 0,
            'inconsistencies': []
        }
        
        try:
            exchanges = ['gate', 'bybit', 'okx']
            consistent_count = 0
            
            for exchange in exchanges:
                ws_file = f"websocket/{exchange}_ws.py"
                if os.path.exists(ws_file):
                    with open(ws_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查时间戳处理方式
                    uses_instance_method = 'self.timestamp_processor.get_synced_timestamp(' in content
                    # 🔥 精确检测：只检查实际的全局调用，排除初始化和注释
                    uses_global_method = False
                    lines = content.split('\n')
                    for line in lines:
                        if 'processor.get_synced_timestamp(' in line:
                            # 排除注释、初始化相关的行
                            if (not line.strip().startswith('#') and
                                'get_timestamp_processor(' not in line and
                                'self.timestamp_processor' not in line):
                                uses_global_method = True
                                break
                    
                    timestamp_results['unified_usage'][exchange] = {
                        'uses_instance_method': uses_instance_method,
                        'uses_global_method': uses_global_method,
                        'consistent': uses_instance_method and not uses_global_method
                    }
                    
                    if uses_instance_method and not uses_global_method:
                        consistent_count += 1
                    else:
                        timestamp_results['inconsistencies'].append({
                            'exchange': exchange,
                            'issue': '时间戳处理方式不一致',
                            'uses_instance': uses_instance_method,
                            'uses_global': uses_global_method
                        })
            
            timestamp_results['consistency_score'] = (consistent_count / len(exchanges)) * 100
            self.results['timestamp_consistency'] = timestamp_results
            
        except Exception as e:
            logger.error(f"时间戳一致性验证失败: {e}")
            
    async def verify_architecture_compliance(self):
        """验证架构合规性"""
        logger.info("🔍 验证架构合规性...")
        
        compliance_results = {
            'bybit_reference': {'simple_run': False, 'no_monitoring': False},
            'gate_compliance': {'matches_bybit': False, 'issues': []},
            'okx_compliance': {'matches_bybit': False, 'issues': []},
            'overall_compliance': 0
        }
        
        try:
            # 检查Bybit作为参考标准
            bybit_file = "websocket/bybit_ws.py"
            if os.path.exists(bybit_file):
                with open(bybit_file, 'r', encoding='utf-8') as f:
                    bybit_content = f.read()
                
                compliance_results['bybit_reference'] = {
                    'simple_run': 'await super().run()' in bybit_content,
                    'no_monitoring': 'def _monitor_data_flow(' not in bybit_content
                }
            
            # 检查其他交易所是否符合Bybit标准
            for exchange in ['gate', 'okx']:
                ws_file = f"websocket/{exchange}_ws.py"
                if os.path.exists(ws_file):
                    with open(ws_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    issues = []
                    
                    # 检查run方法是否简洁
                    if 'await super().run()' not in content:
                        issues.append("run方法不够简洁")
                    
                    # 检查是否有不必要的监控
                    if 'def _monitor_data_flow(' in content:
                        issues.append("存在不必要的监控任务")
                    
                    # 检查是否有并发风险
                    if 'asyncio.gather(' in content and 'subscription_tasks' in content:
                        issues.append("存在并发订阅风险")
                    
                    compliance_key = f"{exchange}_compliance"
                    compliance_results[compliance_key] = {
                        'matches_bybit': len(issues) == 0,
                        'issues': issues
                    }
            
            # 计算整体合规性
            compliant_count = sum(1 for key in ['gate_compliance', 'okx_compliance'] 
                                if compliance_results[key]['matches_bybit'])
            compliance_results['overall_compliance'] = (compliant_count / 2) * 100
            
            self.results['architecture_compliance'] = compliance_results
            
        except Exception as e:
            logger.error(f"架构合规性验证失败: {e}")
            
    async def generate_final_assessment(self):
        """生成最终评估"""
        logger.info("📊 生成最终评估...")
        
        # 统计问题
        websocket_issues = sum(len(ws['issues']) for ws in self.results['websocket_consistency'].values())
        timestamp_consistency = self.results['timestamp_consistency'].get('consistency_score', 0)
        architecture_compliance = self.results['architecture_compliance'].get('overall_compliance', 0)
        
        # 计算总体评分
        overall_score = (
            (100 - websocket_issues * 10) * 0.4 +  # WebSocket一致性权重40%
            timestamp_consistency * 0.3 +           # 时间戳一致性权重30%
            architecture_compliance * 0.3           # 架构合规性权重30%
        )
        
        # 确定等级
        if overall_score >= 95:
            grade = "EXCELLENT"
            status = "生产就绪"
        elif overall_score >= 85:
            grade = "GOOD"
            status = "基本就绪"
        elif overall_score >= 70:
            grade = "FAIR"
            status = "需要改进"
        else:
            grade = "POOR"
            status = "需要重大修复"
        
        self.results['final_assessment'] = {
            'overall_score': round(overall_score, 2),
            'grade': grade,
            'status': status,
            'websocket_issues_count': websocket_issues,
            'timestamp_consistency_score': timestamp_consistency,
            'architecture_compliance_score': architecture_compliance,
            'ready_for_production': overall_score >= 90
        }
        
        # 输出详细结果
        logger.info("=" * 80)
        logger.info("🎯 最终修复验证结果")
        logger.info("=" * 80)
        logger.info(f"📊 总体评分: {overall_score:.2f}/100")
        logger.info(f"🏆 等级: {grade}")
        logger.info(f"🚀 状态: {status}")
        logger.info(f"🔧 WebSocket问题: {websocket_issues}个")
        logger.info(f"⏰ 时间戳一致性: {timestamp_consistency:.1f}%")
        logger.info(f"🏗️ 架构合规性: {architecture_compliance:.1f}%")
        logger.info(f"✅ 生产就绪: {'是' if overall_score >= 90 else '否'}")

        # 输出详细问题
        if websocket_issues > 0:
            logger.info("\n🔍 WebSocket问题详情:")
            for ws_name, ws_data in self.results['websocket_consistency'].items():
                if ws_data['issues']:
                    logger.info(f"  - {ws_name}: {', '.join(ws_data['issues'])}")

        if timestamp_consistency < 100:
            logger.info("\n⏰ 时间戳一致性问题:")
            for inconsistency in self.results['timestamp_consistency'].get('inconsistencies', []):
                logger.info(f"  - {inconsistency['exchange']}: {inconsistency['issue']}")

        if overall_score >= 90:
            logger.info("🎉 恭喜！系统修复达到生产就绪标准！")
        else:
            logger.warning("⚠️ 系统仍需进一步优化才能达到生产标准")


async def main():
    """主函数"""
    try:
        verification = FinalFixVerification()
        results = await verification.run_verification()
        
        # 保存验证结果
        output_file = f"final_fix_verification_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"📄 验证结果已保存到: {output_file}")
        
        return results
        
    except Exception as e:
        logger.error(f"最终验证失败: {e}")
        return None


if __name__ == "__main__":
    asyncio.run(main())
