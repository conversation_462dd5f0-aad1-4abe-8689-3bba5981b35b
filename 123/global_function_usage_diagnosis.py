#!/usr/bin/env python3
"""
全局函数使用情况精确诊断脚本
根据修复提示词的8点内部检查清单进行深度分析
"""

import os
import re
import json
from typing import Dict, List, Any
from datetime import datetime

def analyze_global_function_usage():
    """分析全局get_synced_timestamp函数的使用情况"""
    
    print("🔍 开始分析全局函数使用情况...")
    
    # 要检查的核心模块
    core_modules = [
        "core/opportunity_scanner.py",
        "core/unified_order_spread_calculator.py",
        "core/data_snapshot_validator.py",
        "websocket/unified_data_formatter.py",
        "websocket/unified_timestamp_processor.py"
    ]
    
    results = {
        "timestamp": datetime.now().isoformat(),
        "global_function_calls": [],
        "instance_method_calls": [],
        "analysis_summary": {},
        "critical_issues": []
    }
    
    # 检查每个模块
    for module_path in core_modules:
        full_path = module_path  # 直接使用相对路径，因为已经在123目录下
        if not os.path.exists(full_path):
            print(f"⚠️ 文件不存在: {full_path}")
            continue
            
        print(f"📁 检查模块: {module_path}")
        
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
        # 查找全局函数调用（排除方法定义）
        global_pattern = r'(?<!def\s)(?<!self\.)get_synced_timestamp\s*\(\s*["\']?\w+["\']?\s*,'
        instance_pattern = r'\.get_synced_timestamp\s*\('
        
        for i, line in enumerate(lines, 1):
            # 跳过注释行
            if line.strip().startswith('#'):
                continue
                
            # 检查全局函数调用
            if re.search(global_pattern, line):
                results["global_function_calls"].append({
                    "file": module_path,
                    "line": i,
                    "code": line.strip(),
                    "type": "global_call"
                })
                
            # 检查实例方法调用
            if re.search(instance_pattern, line):
                results["instance_method_calls"].append({
                    "file": module_path,
                    "line": i, 
                    "code": line.strip(),
                    "type": "instance_call"
                })
    
    # 分析统计
    total_global = len(results["global_function_calls"])
    total_instance = len(results["instance_method_calls"])
    
    results["analysis_summary"] = {
        "total_global_calls": total_global,
        "total_instance_calls": total_instance,
        "consistency_percentage": (total_instance / (total_global + total_instance) * 100) if (total_global + total_instance) > 0 else 0,
        "modules_with_global_calls": len(set(call["file"] for call in results["global_function_calls"])),
        "modules_with_instance_calls": len(set(call["file"] for call in results["instance_method_calls"]))
    }
    
    # 识别关键问题
    if total_global > 0:
        results["critical_issues"].append({
            "issue": "全局函数调用存在",
            "description": f"发现{total_global}处全局get_synced_timestamp调用，可能绕过数据新鲜度检查",
            "severity": "HIGH",
            "affected_modules": list(set(call["file"] for call in results["global_function_calls"]))
        })
    
    # 检查unified_timestamp_processor.py中的全局函数实现
    processor_path = "websocket/unified_timestamp_processor.py"
    if os.path.exists(processor_path):
        with open(processor_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找全局函数定义中的processor.get_synced_timestamp调用
        if "processor.get_synced_timestamp(data)" in content:
            results["critical_issues"].append({
                "issue": "全局函数内部调用不一致",
                "description": "unified_timestamp_processor.py中的全局函数仍使用processor.get_synced_timestamp(data)调用",
                "severity": "CRITICAL",
                "location": "websocket/unified_timestamp_processor.py:769"
            })
    
    return results

def generate_8_point_checklist_analysis(results: Dict[str, Any]):
    """根据8点内部检查清单进行分析"""
    
    print("\n📋 8点内部检查清单分析:")
    
    checklist = {
        "1_existing_architecture": {
            "question": "现有架构中是否已有此功能？",
            "answer": "是，统一时间戳处理器已存在",
            "status": "✅ 通过"
        },
        "2_unified_module": {
            "question": "是否应该在统一模块中实现？", 
            "answer": f"发现{results['analysis_summary']['total_global_calls']}处全局调用，需要统一为实例方法",
            "status": "❌ 不通过" if results['analysis_summary']['total_global_calls'] > 0 else "✅ 通过"
        },
        "3_root_cause": {
            "question": "问题的根本原因是什么？",
            "answer": "全局函数调用绕过了数据新鲜度检查机制，导致Gate.io和OKX报告时间戳过期而Bybit不报告",
            "status": "✅ 已识别"
        },
        "4_interface_check": {
            "question": "检查链路和接口的结果是什么？",
            "answer": f"发现{len(results['critical_issues'])}个关键问题，接口调用不一致",
            "status": "❌ 存在问题" if len(results['critical_issues']) > 0 else "✅ 正常"
        },
        "5_other_exchanges": {
            "question": "其他两个交易所是否有同样问题？",
            "answer": "三个交易所WebSocket都已使用实例方法，但核心模块仍有全局调用",
            "status": "⚠️ 部分问题"
        },
        "6_optimal_solution": {
            "question": "如何从源头最优解决问题？",
            "answer": "将所有全局get_synced_timestamp调用改为实例方法调用",
            "status": "📋 待修复"
        },
        "7_duplication_check": {
            "question": "是否重复调用，存在造轮子？",
            "answer": "存在全局函数和实例方法的重复实现",
            "status": "⚠️ 需要整合"
        },
        "8_comprehensive_review": {
            "question": "横向深度全面查阅资料并思考？",
            "answer": "基于07B文档，需要确保三交易所时间戳处理完全一致",
            "status": "✅ 已审查"
        }
    }
    
    for key, item in checklist.items():
        print(f"{item['status']} {item['question']}")
        print(f"   答案: {item['answer']}")
    
    return checklist

def main():
    """主函数"""
    print("🚀 全局函数使用情况精确诊断")
    print("=" * 60)
    
    # 分析全局函数使用情况
    results = analyze_global_function_usage()
    
    # 输出结果
    print(f"\n📊 分析结果:")
    print(f"全局函数调用: {results['analysis_summary']['total_global_calls']}处")
    print(f"实例方法调用: {results['analysis_summary']['total_instance_calls']}处") 
    print(f"一致性百分比: {results['analysis_summary']['consistency_percentage']:.1f}%")
    
    if results["global_function_calls"]:
        print(f"\n🔥 发现的全局函数调用:")
        for call in results["global_function_calls"]:
            print(f"  📁 {call['file']}:{call['line']} - {call['code']}")
    
    if results["critical_issues"]:
        print(f"\n⚠️ 关键问题:")
        for issue in results["critical_issues"]:
            print(f"  🔥 {issue['severity']}: {issue['issue']}")
            print(f"     {issue['description']}")
    
    # 8点检查清单分析
    checklist = generate_8_point_checklist_analysis(results)
    
    # 保存结果
    output_file = f"global_function_diagnosis_{int(datetime.now().timestamp())}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            "results": results,
            "checklist": checklist
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 诊断结果已保存到: {output_file}")
    
    # 修复建议
    print(f"\n🔧 修复建议:")
    if results['analysis_summary']['total_global_calls'] > 0:
        print("1. 将所有全局get_synced_timestamp调用改为实例方法调用")
        print("2. 确保所有核心模块都使用self.timestamp_processor.get_synced_timestamp()")
        print("3. 验证修复后三交易所时间戳处理完全一致")
    else:
        print("✅ 未发现全局函数调用问题")

if __name__ == "__main__":
    main()
