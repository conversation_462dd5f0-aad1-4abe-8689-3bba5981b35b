2025-08-03 14:47:26 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-03 14:47:26 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-03 14:47:26 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-03 14:47:26 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-03 14:47:26.383 [INFO] [exchanges.okx_exchange] 🚨 OKX API限制优化为2次/秒，解决WebSocket阻塞问题
2025-08-03 14:47:26.383 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-03 14:47:26 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-03 14:47:26 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-03 14:47:26 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-03 14:47:26 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-03 14:47:26.829 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:47:27.160 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:47:27.160 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:47:27.481 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:47:27.481 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:47:27 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-08-03 14:47:27.482 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:47:27.829 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:47:27.830 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:47:28.144 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:47:28.145 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:47:28 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-08-03 14:47:28.145 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:47:28.476 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:47:28.476 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:47:28.813 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:47:28.813 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:47:28 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-08-03 14:47:28.814 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:47:29.134 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:47:29.134 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:47:29.472 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:47:29.473 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:47:29 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-08-03 14:47:29 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-03 14:47:29.800 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 14:47:31.168 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 14:47:32.685 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 14:47:33.343 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 14:47:33.915 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 14:47:34.244 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 14:47:34.827 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 14:47:35.396 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 14:47:55.244 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:47:55.244 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:47:55.902 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:47:55.902 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:47:56.563 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:47:56.563 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:47:57.220 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:47:57.220 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:47:57.886 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:47:57.886 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:47:58.561 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:47:58.561 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:40.750 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 14:48:41.088 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 14:48:41.090 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-08-03 14:48:41.113 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 14:48:41.122 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 14:48:43.525 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-03 14:48:43.853 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-08-03 14:48:43.857 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-03 14:48:43.858 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-08-03 14:48:43.900 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-03 14:48:44.902 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:48:45.236 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:48:45.236 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:48:45.236 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:48:45.236 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:48:45.237 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:48:45.237 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:48:45.237 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:48:45.237 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:48:45.237 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 14:48:45.316 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:48:45.317 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:45.642 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:48:45.643 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:45.643 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:48:45.643 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:45.652 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:48:45.652 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:45.653 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:48:45.653 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:45.658 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 14:48:45.659 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 14:48:45.659 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 14:48:45.659 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 14:48:45.660 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 14:48:45.660 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 14:48:45.660 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 14:48:45.660 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 14:48:45 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 14:48:45.676 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 14:48:45.676 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 14:48:45.676 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 14:48:45.677 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 14:48:45.677 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 14:48:45.677 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 14:48:45.677 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 14:48:45.678 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 14:48:45 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 14:48:45.680 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 14:48:45 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 14:48:45.680 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:48:45.680 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:48:45.685 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 14:48:45.686 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 14:48:45.686 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 14:48:45.686 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 14:48:45.686 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 14:48:45.686 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 14:48:45.687 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 14:48:45.687 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 14:48:45 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 14:48:45.712 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 14:48:45.712 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 14:48:45 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 14:48:45.977 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:48:45.977 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:48:45.979 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:48:45.979 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:48:45.984 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:48:45.985 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:48:45.996 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:48:45.997 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:48:47.729 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:48:47.729 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:48.065 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:48:48.065 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:48.071 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:48:48.071 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:48.075 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:48:48.075 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:48.088 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:48:48.088 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:48:48.096 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-03 14:48:48.096 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 14:48:48.394 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:48:48.394 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:48:48.400 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:48:48.401 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:48:48.401 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:48:48.401 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:48:48.406 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 14:48:48.406 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 14:48:59.212 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 14:49:00.719 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 14:49:02.226 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 14:49:02.883 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 14:49:03.461 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 14:49:03.790 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 14:49:04.356 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 14:49:04.933 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 14:49:09.717 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 14:49:11.281 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 14:49:12.732 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 14:49:13.405 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 14:49:14.005 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 14:49:14.331 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 14:49:14.913 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 14:49:15.492 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
