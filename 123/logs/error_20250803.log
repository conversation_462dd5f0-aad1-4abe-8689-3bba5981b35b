2025-08-03 14:47:33.915 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 14:47:35.396 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 14:48:45.658 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 14:48:45.659 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 14:48:45.659 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 14:48:45.659 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 14:48:45.660 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 14:48:45.660 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 14:48:45.660 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 14:48:45.660 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 14:48:45.676 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 14:48:45.676 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 14:48:45.676 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 14:48:45.677 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 14:48:45.677 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 14:48:45.677 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 14:48:45.677 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 14:48:45.678 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 14:48:45.679 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 14:48:45.680 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 14:48:45.685 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 14:48:45.686 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 14:48:45.686 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 14:48:45.686 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 14:48:45.686 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 14:48:45.686 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 14:48:45.687 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 14:48:45.687 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 14:48:45.712 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 14:48:45.712 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 14:48:45.713 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 14:48:46.817 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-03 14:48:46.817 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-03 14:48:46.817 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-03 14:48:59.296 [ERROR] [websocket] [GATE] ❌ [GATE-FUTURES] 订阅失败: {'time': **********, 'time_ms': *************, 'conn_id': '93d1647db6e1ecf9', 'trace_id': 'bcb98a5cb8794909ef3e30fb02fcebce', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}
2025-08-03 14:48:59.296 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'futures.order_book', 'market_type': 'futures', 'error_message': "{'time': **********, 'time_ms': *************, 'conn_id': '93d1647db6e1ecf9', 'trace_id': 'bcb98a5cb8794909ef3e30fb02fcebce', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}"}
2025-08-03 14:49:03.461 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 14:49:04.073 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-03 14:49:04.074 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-03 14:49:04.310 [ERROR] [websocket] [GATE] ❌ [GATE-SPOT] 订阅失败: {'time': 1754225344, 'time_ms': 1754225344244, 'conn_id': '5bbdcb6d37237392', 'trace_id': '98bf75ab09bd1b900b0c29572e98317e', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '98bf75ab09bd1b900b0c29572e98317e'}
2025-08-03 14:49:04.310 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': 1754225344, 'time_ms': 1754225344244, 'conn_id': '5bbdcb6d37237392', 'trace_id': '98bf75ab09bd1b900b0c29572e98317e', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '98bf75ab09bd1b900b0c29572e98317e'}"}
2025-08-03 14:49:04.933 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 14:49:14.005 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 14:49:15.492 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 14:49:27.244 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-03 14:49:27.293 [ERROR] [websocket] [GATE] ❌ [GATE-SPOT] 订阅失败: {'time': 1754225367, 'time_ms': 1754225367169, 'conn_id': 'b1109eebf64d840d', 'trace_id': '370669666903822a37b7f622d35aa13d', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '370669666903822a37b7f622d35aa13d'}
2025-08-03 14:49:27.293 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': 1754225367, 'time_ms': 1754225367169, 'conn_id': 'b1109eebf64d840d', 'trace_id': '370669666903822a37b7f622d35aa13d', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '370669666903822a37b7f622d35aa13d'}"}
2025-08-03 14:49:27.298 [ERROR] [websocket] [GATE] ❌ [GATE-FUTURES] 订阅失败: {'time': 1754225367, 'time_ms': 1754225367170, 'conn_id': '38d477208af17304', 'trace_id': '58a36a193fb422f4f3143af8dc93e2db', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}
2025-08-03 14:49:27.298 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'futures.order_book', 'market_type': 'futures', 'error_message': "{'time': 1754225367, 'time_ms': 1754225367170, 'conn_id': '38d477208af17304', 'trace_id': '58a36a193fb422f4f3143af8dc93e2db', 'channel': 'futures.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}}"}
2025-08-03 14:49:27.401 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-03 14:50:21.169 [ERROR] [asyncio] Future exception was never retrieved
future: <Future finished exception=ConnectionClosedError(None, Close(code=1000, reason=''), None)>
websockets.exceptions.ConnectionClosedError: sent 1000 (OK); no close frame received
2025-08-03 14:50:34.541 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.557 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.558 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.559 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.561 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.561 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.562 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.563 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.563 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.564 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.565 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.566 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.567 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.568 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.568 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.569 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.570 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.570 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.571 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.572 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.573 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.573 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.574 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.575 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.575 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.576 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.577 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.577 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.578 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.579 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.580 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.580 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.581 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.582 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.583 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.584 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.584 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.585 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.586 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.586 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.587 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.588 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.589 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.589 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.590 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.591 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.592 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.592 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.593 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.594 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.594 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.595 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.596 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.596 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.597 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.598 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.598 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.600 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.600 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.601 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:34.602 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:36.323 [ERROR] [websocket] [OKX] 连接超时，超过15秒
2025-08-03 14:50:36.365 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:36.366 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:36.367 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:36.368 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:36.369 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 651, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-03 14:50:36.370 [ERROR] [websocket] [OKX] 连接超时，超过15秒
2025-08-03 14:50:36.690 [ERROR] [websocket] [OKX] 连接错误: server rejected WebSocket connection: HTTP 503
Traceback (most recent call last):
  File "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/ws_client.py", line 268, in _connect
    self.ws = await asyncio.wait_for(
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 655, in __await_impl_timeout__
    return await self.__await_impl__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 662, in __await_impl__
    await protocol.handshake(
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 329, in handshake
    raise InvalidStatusCode(status_code, response_headers)
websockets.exceptions.InvalidStatusCode: server rejected WebSocket connection: HTTP 503
2025-08-03 14:50:38.242 [ERROR] [websocket.error_handler] [OKX] connection_error: server rejected WebSocket connection: HTTP 503
