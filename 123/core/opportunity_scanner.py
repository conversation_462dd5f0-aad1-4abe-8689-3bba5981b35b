#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
套利机会扫描器 - 负责实时扫描和识别套利机会
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass, field
from decimal import Decimal
import threading
from collections import defaultdict

from config.settings import get_config
from utils.logger import get_logger
from utils.helpers import calculate_percentage, format_currency

# 🔥 修复：延迟导入WebSocket模块，避免循环依赖
# WebSocket模块将在需要时动态导入


@dataclass
class ArbitrageOpportunity:
    """套利机会数据结构"""
    # 基础信息
    symbol: str                    # 交易对，如ADA-USDT
    base_amount: float            # 基准数量（如0.0001 BTC）

    # 交易所和价格信息
    exchange1_name: str           # 交易所1名称
    exchange1_market: str         # 交易所1市场类型（spot/futures）
    exchange1_price: float        # 交易所1价格
    exchange1_value: float        # 交易所1该数量的价值（USDT）

    exchange2_name: str           # 交易所2名称
    exchange2_market: str         # 交易所2市场类型（spot/futures）
    exchange2_price: float        # 交易所2价格
    exchange2_value: float        # 交易所2该数量的价值（USDT）

    # 套利数据
    spread_value: float           # 价差（USDT）
    spread_percent: float         # 价差百分比
    profit_estimate: float        # 预估利润（USDT）

    # 执行方向
    buy_exchange: str            # 买入交易所
    buy_market: str              # 买入市场类型
    sell_exchange: str           # 卖出交易所
    sell_market: str             # 卖出市场类型

    # 时间戳
    timestamp: int               # 发现时间戳


@dataclass
class MarketData:
    """市场数据结构 - ticker字段已移除"""
    exchange: str
    symbol: str
    price: float
    timestamp: int  # 🔥 关键修复：统一为毫秒级整数时间戳，确保类型一致性
    orderbook: Dict = field(default_factory=dict)
    # ticker字段已完全移除
    # 🔥 修复：添加数据完整性标记字段，解决ExecutionEngine中"unknown"状态问题
    incomplete_orderbook: bool = False
    incomplete_reason: str = ""


@dataclass
class CombinationConfig:
    """套利组合配置"""
    name: str
    spot_exchange: str
    futures_exchange: str
    enabled: bool = True
    min_volume: float = 100.0
    max_volume: float = 5000.0


class OpportunityScanner:
    """
    🔥 WebSocket优化说明：
    - 统一使用WebSocketManager管理连接
    - OpportunityScanner只接收数据，不管理连接
    - 避免重复的WebSocket初始化逻辑

    套利机会扫描器
    """


    # 🔥 删除重复实现：统一使用Order差价计算，不再保留简单计算方法

    # 🔥 删除重复实现：不再需要包装方法，直接使用UnifiedOrderSpreadCalculator
    # 所有调用都应该直接使用 get_order_spread_calculator().calculate_order_based_spread()
    # 这个方法已被删除，避免重复调用和接口不统一问题

    def _validate_price_reasonableness(self, symbol: str, exchange: str, market_type: str, new_price: float) -> bool:
        """🔥 新增：价格合理性验证，防止异常价格跳跃影响套利决策"""
        try:
            # 获取历史价格数据
            data_key = f"{exchange}_{market_type}_{symbol}"

            if data_key in self.market_data:
                last_price = self.market_data[data_key].price

                if last_price > 0:
                    # 计算价格变化百分比
                    price_change_pct = abs(new_price - last_price) / last_price

                    # 5%变化阈值 - 超过此阈值认为价格异常
                    if price_change_pct > 0.05:
                        self.logger.warning(f"🚨 价格异常跳跃: {data_key} {last_price:.6f} -> {new_price:.6f} ({price_change_pct*100:.2f}%)")
                        return False

                    # 记录正常的价格变化（仅在调试模式下）
                    if price_change_pct > 0.01:  # 1%以上的变化记录
                        self.logger.debug(f"📊 价格变化: {data_key} {last_price:.6f} -> {new_price:.6f} ({price_change_pct*100:.2f}%)")

            return True

        except Exception as e:
            self.logger.error(f"价格合理性验证失败: {e}")
            return True  # 验证失败时允许价格更新，避免阻塞系统

    def __init__(self, config=None):
        """
        初始化机会扫描器

        Args:
            config: 可选配置对象，如果为None则使用默认配置
        """
        self.config = config if config is not None else get_config()
        self.logger = get_logger(self.__class__.__name__)

        # 🔥 修复：删除硬编码代币，使用通用代币系统
        from core.universal_token_system import get_universal_token_system
        token_system = get_universal_token_system()
        supported_symbols = token_system.get_supported_symbols()
        self.symbol = supported_symbols[0] if supported_symbols else self.config.get('TARGET_SYMBOL', 'ADA-USDT')
        self.min_spread = float(self.config.get('MIN_SPREAD', 0.001))  # 🔥 修复：0.1%，与.env保持一致
        self.max_spread = float(self.config.get('MAX_SPREAD', 0.05))   # 5%
        
        # 🔥 重构：使用USDT金额而不是硬编码币数量
        # 不再使用ARBITRAGE_BASE_AMOUNT（这是BTC时代的遗留配置）
        # 改为使用MIN_ORDER_AMOUNT_USD来动态计算任意币种的数量
        # 🔥 修复：强制从.env读取配置，不使用错误的默认值
        try:
            import os
            # 🔥 直接从环境变量读取，确保使用.env中的配置
            min_order_env = os.getenv('MIN_ORDER_AMOUNT_USD')
            if min_order_env:
                self.min_order_amount_usd = float(min_order_env)
                self.logger.info(f"✅ 从.env读取MIN_ORDER_AMOUNT_USD: ${self.min_order_amount_usd}")
            else:
                # 🔥 如果.env中没有配置，使用.env中的默认值35.0
                self.min_order_amount_usd = 35.0
                self.logger.warning(f"⚠️ .env中未找到MIN_ORDER_AMOUNT_USD，使用默认值: ${self.min_order_amount_usd}")
        except Exception as e:
            self.logger.error(f"❌ 读取MIN_ORDER_AMOUNT_USD配置失败: {e}")
            # 🔥 应急默认值也使用35.0，与.env保持一致
            self.min_order_amount_usd = 35.0
            self.logger.error(f"使用应急默认值: ${self.min_order_amount_usd}")

        # 验证订单金额有效性
        try:
            if self.min_order_amount_usd <= 0 or self.min_order_amount_usd != self.min_order_amount_usd:  # 检查NaN
                self.logger.error(f"配置的MIN_ORDER_AMOUNT_USD无效: {self.min_order_amount_usd}，系统无法运行")
                raise ValueError(f"MIN_ORDER_AMOUNT_USD配置无效: {self.min_order_amount_usd}")

            # 严格验证：必须从.env配置读取，不允许使用硬编码范围
            # 这是多币种系统的关键，订单金额必须可配置
            min_allowed = float(self.config.get('MIN_CAPITAL_REQUIREMENT', '10.0')) / 10  # 至少是最小资金的1/10
            max_allowed = float(self.config.get('MAX_ORDER_AMOUNT_USD', '1000.0'))  # 不超过最大限制

            if self.min_order_amount_usd < min_allowed:
                self.logger.error(f"min_order_amount_usd {self.min_order_amount_usd} 低于系统要求 {min_allowed}")
                raise ValueError(f"MIN_ORDER_AMOUNT_USD配置过小")
            elif self.min_order_amount_usd > max_allowed:
                self.logger.error(f"min_order_amount_usd {self.min_order_amount_usd} 超过系统限制 {max_allowed}")
                raise ValueError(f"MIN_ORDER_AMOUNT_USD配置过大")

        except (ValueError, TypeError, OverflowError) as e:
            self.logger.error(f"解析MIN_ORDER_AMOUNT_USD失败: {e}，系统无法继续运行")
            raise RuntimeError(f"关键配置MIN_ORDER_AMOUNT_USD解析失败，多币种系统需要有效的订单金额配置")

        # 🔥 删除重复配置读取：使用通用代币系统
        from core.universal_token_system import get_universal_token_system
        token_system = get_universal_token_system()
        self.supported_symbols = token_system.get_supported_symbols()
        self.logger.info(f"OpportunityScanner支持的多币种交易对: {self.supported_symbols}")
        
        # 确保支持多币种，而不是单一symbol
        if len(self.supported_symbols) == 1:
            self.logger.warning("⚠️ 当前只配置了一个交易对，这不是真正的多币种系统")
        
        # 移除单一symbol配置，改为多币种支持
        self.symbol = None  # 不再使用单一symbol
        
        # 🚀 移除硬编码配置，使用完全通用的价格区间算法
        # 系统将根据实时价格自动判断精度和最小值，无需硬编码币种
        self.logger.info("采用通用价格区间算法，支持任意币种无需修改代码")
        
        # 🚨 为了兼容性，仍然设置一个base_amount（但这个值会在实际执行时动态计算）
        # 这里设置为一个占位符，真正的交易量会根据当时的价格动态计算
        self.base_amount = 0.001  # 这只是一个占位符，不会直接用于交易
        
        self.logger.info(f"机会扫描器初始化: 多币种支持, min_order_amount_usd=${self.min_order_amount_usd}, min_spread={self.min_spread*100:.2f}%")

        # 🔥 **CRITICAL修复**：添加统一时间戳处理器实例，解决全局函数调用问题
        from websocket.unified_timestamp_processor import get_timestamp_processor
        self.timestamp_processor = get_timestamp_processor("system")

        # WebSocket客户端
        self.ws_clients: Dict[str, object] = {}

        # 市场数据存储
        self.market_data: Dict[str, MarketData] = {}
        # 🔥 根本修复：使用可重入锁，允许同一线程多次获取锁，解决嵌套锁定死锁问题
        self.data_lock = threading.RLock()

        # 🔥 VPS修复：专门的价格日志器，完全独立
        self.websocket_price_logger = logging.getLogger('websocket_prices_only')
        self.websocket_orderbook_logger = logging.getLogger('websocket_orderbook')

        # 🔥 修复：确保所有必需属性在__init__中正确初始化
        # 运行状态
        self.running = False
        self.last_scan_time = 0

        # 🎯 统一扫描间隔配置：使用.env中的SCAN_INTERVAL - 🔥 修复：统一配置参数名称
        try:
            self.scan_interval = float(self.config.get('SCAN_INTERVAL', '0.3'))
        except (ValueError, TypeError):
            self.scan_interval = 0.3  # 🔥 修复：默认300ms，与用户要求一致
            self.logger.warning("SCAN_INTERVAL配置无效，使用默认值0.3秒")

        # 🎯 统一日志间隔配置：使用.env中的LOG_INTERVAL
        try:
            self.log_interval = float(self.config.get('LOG_INTERVAL', '0.3'))
        except (ValueError, TypeError):
            self.log_interval = 0.3  # 默认300ms，避免刷屏
            self.logger.warning("LOG_INTERVAL配置无效，使用默认值0.3秒")

        # 🔥 关键修复：确保last_opportunity_log属性始终初始化
        self.last_opportunity_log: Dict[str, float] = {}

        # 🔥 关键修复：初始化所有可能缺失的属性，解决AttributeError
        self._last_price_log = {}         # OKX数据处理必需
        self._last_price_update_log = {}  # price update日志控制
        self._data_update_counter = 0     # 数据更新计数器

        # 🔥 用户要求：初始化网络延迟监控器 - 复用现有RiskMonitor的延迟检测功能
        self._risk_monitor = None  # 延迟初始化，避免循环依赖

        # 当前套利机会
        self.current_opportunities: List[ArbitrageOpportunity] = []

        # 套利组合配置
        self.combinations = self._init_combinations()
        self.available_combinations: List[CombinationConfig] = []

        self.logger.info("OpportunityScanner初始化完成")

    # 🔥 根源修复：删除重复的日志记录机制
    # 原_log_websocket_price_data方法已删除，统一使用_log_arbitrage_opportunity_websocket
    # 避免重复日志、接口不统一、数据不一致等问题

    def _log_arbitrage_opportunity_websocket(self, symbol: str, spot_exchange: str, futures_exchange: str,
                                           spot_price: float, futures_price: float, spread_percent: float):
        """
        🔥 根源修复：统一的期现套利日志记录 - 确保100%数据一致性

        优化特性：
        - 智能去重：基于差价变化(0.01%)和时间间隔(0.05秒)
        - 实时记录：LOG_INTERVAL可配置，默认0.5秒
        - 精准数据：使用Order加权平均价格，与差价计算100%一致
        """
        try:
            # 🔥 统一Order差价计算：日志记录使用传入的Order差价计算结果
            # 传入的spread_percent已经是Order差价计算的结果，直接使用
            # 删除重复的价格计算，确保数据一致性

            # 🔥 智能去重机制：基于差价变化的实时日志记录
            log_key = f"{symbol}_{spot_exchange}_{futures_exchange}"
            current_time = time.time()

            # 🔥 **用户强制要求**：显示100%真实数据，移除所有去重和频率限制
            # 完全关闭日志去重策略，确保显示所有真实扫描数据
            # 🚨 用户需要看到所有差价变化来判断正确性
            # 移除所有return语句，强制记录所有日志

            # 🔥 根据全流程工作流文档的6种期现套利组合
            combo_mapping = {
                ("gate", "bybit"): ("A", "Gate现货 ↔ Bybit期货"),
                ("bybit", "gate"): ("B", "Bybit现货 ↔ Gate期货"),
                ("okx", "bybit"): ("C", "OKX现货 ↔ Bybit期货"),
                ("bybit", "okx"): ("D", "Bybit现货 ↔ OKX期货"),
                ("okx", "gate"): ("E", "OKX现货 ↔ Gate期货"),
                ("gate", "okx"): ("F", "Gate现货 ↔ OKX期货")
            }

            combo_info = combo_mapping.get((spot_exchange.lower(), futures_exchange.lower()), ("?", "未知组合"))
            combo_id, combo_desc = combo_info

            # 🔥 根源修复：统一emoji使用，避免重复日志中的emoji混乱
            if spread_percent > 0:
                # 期货溢价（期货价格 > 现货价格）= 发现差价！
                emoji = "🚀"  # 🔥 统一使用🚀表示发现差价
                sign = "+"
                premium_type = "期货溢价"  # 发现差价！
                action = f"买{spot_exchange.upper()}现货+卖{futures_exchange.upper()}期货"
            elif spread_percent < 0:
                # 现货溢价（现货价格 > 期货价格）= 趋同！
                emoji = "🚀"  # 🔥 统一使用🚀，避免emoji不一致
                sign = ""  # 负号已包含在数值中
                premium_type = "现货溢价"  # 趋同！
                action = f"卖{spot_exchange.upper()}现货+买{futures_exchange.upper()}期货"
            else:
                # 无差价
                emoji = "⚖️"
                sign = ""
                premium_type = "无差价"
                action = "无套利机会"

            # 🔥 修复3：优化格式对齐，确保整整齐齐
            spread_value = abs(spread_percent * 100)

            # 统一交易所名称长度，确保对齐
            spot_name = spot_exchange.upper()[:5].ljust(5)
            futures_name = futures_exchange.upper()[:5].ljust(5)

            # 🔥 用户要求：消息末尾添加毫秒信息 (ms.xxx) 和网络延迟诊断
            current_ms = int((current_time % 1) * 1000)  # 获取当前时间的毫秒部分

            # 🔥 用户要求：获取网络延迟信息 - 复用现有RiskMonitor功能
            try:
                # 同步获取两个交易所的延迟（避免异步阻塞）
                spot_lat = self._get_network_latency(spot_exchange)
                futures_lat = self._get_network_latency(futures_exchange)

                if spot_lat > 0 or futures_lat > 0:
                    latency_info = f"lat:{spot_lat:.0f}ms/{futures_lat:.0f}ms"
                else:
                    latency_info = "lat:--ms/--ms"
            except:
                latency_info = "lat:--ms/--ms"

            # 🔥 **用户强制要求**：增强日志格式，显示完整的扫描信息
            # 添加数据源信息、时间戳、深度信息等，便于判断差价正确性
            from datetime import datetime
            timestamp_str = datetime.now().strftime("%H:%M:%S.%f")[:-3]  # 精确到毫秒
            
            # 🔥 统一精度：价格和差价都使用8位小数，确保最高精度
            log_message = (
                f"{timestamp_str} {emoji} [{combo_id}] {symbol:<12} | "
                f"{spot_name}现货${spot_price:>12.8f} ↔ "
                f"{futures_name}期货${futures_price:>12.8f} | "
                f"差价{sign}{spread_value:>10.8f}% | "
                f"{premium_type} | {action} | "
                f"数据源:WebSocket | {latency_info}"
            )

            # 记录到websocket_prices.log（这个会被终端显示）
            self.websocket_price_logger.info(log_message)

        except Exception as e:
            # 🔥 修复：不再静默处理日志错误，输出详细错误信息用于调试
            self.logger.error(f"❌ 日志记录失败: {e}")
            self.logger.error(f"   参数: symbol={symbol}, spot_exchange={spot_exchange}, futures_exchange={futures_exchange}")
            self.logger.error(f"   价格: spot_price={spot_price}, futures_price={futures_price}, spread_percent={spread_percent}")
            import traceback
            self.logger.error(f"   堆栈: {traceback.format_exc()}")

    def _get_network_latency(self, exchange: str) -> float:
        """🔥 修复：获取网络延迟信息 - 实现简单有效的延迟检测"""
        try:
            # 🔥 修复：使用简单直接的延迟检测，避免复杂的RiskMonitor依赖
            # 检查是否有缓存的延迟数据
            if not hasattr(self, '_latency_cache'):
                self._latency_cache = {}
                self._last_latency_check = {}

            current_time = time.time()
            cache_key = exchange.lower()

            # 🔥 缓存延迟数据5秒，避免频繁检测
            if (cache_key in self._latency_cache and
                cache_key in self._last_latency_check and
                current_time - self._last_latency_check[cache_key] < 5.0):
                return self._latency_cache[cache_key]

            # 🔥 修复：实现简单的延迟检测
            latency = self._measure_exchange_latency(exchange)

            # 更新缓存
            self._latency_cache[cache_key] = latency
            self._last_latency_check[cache_key] = current_time

            return latency

        except Exception as e:
            # 静默处理异常，返回默认值
            return 0.0

    def _validate_stored_data_quality(self, data_key: str, stored_data: MarketData):
        """🔥 新增：验证存储数据的质量"""
        try:
            # 验证基本字段
            if not stored_data.exchange or not stored_data.symbol:
                self.logger.warning(f"⚠️ 存储数据缺少基本字段: {data_key}")
                return False
                
            # 验证价格合理性
            if stored_data.price <= 0:
                self.logger.warning(f"⚠️ 存储数据价格无效: {data_key}, price={stored_data.price}")
                return False
                
            # 验证时间戳新鲜度（5秒内）
            # 🔥 关键修复：使用统一数据年龄计算函数，解决时间戳单位不一致问题
            from websocket.unified_timestamp_processor import calculate_data_age
            current_time = time.time()
            data_age_seconds = calculate_data_age(stored_data.timestamp, current_time)
            data_age = data_age_seconds * 1000  # 转换为毫秒用于比较
            if data_age > 5000:  # 5秒
                self.logger.warning(f"⚠️ 存储数据过期: {data_key}, age={data_age:.0f}ms")
                return False
                
            # 验证订单簿数据（如果存在）
            if stored_data.orderbook:
                asks = stored_data.orderbook.get('asks', [])
                bids = stored_data.orderbook.get('bids', [])
                if not asks and not bids:
                    self.logger.warning(f"⚠️ 存储数据订单簿为空: {data_key}")
                    return False
                    
            self.logger.debug(f"✅ 存储数据质量验证通过: {data_key}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 数据质量验证异常: {data_key} - {e}")
            return False
            
    def _handle_data_storage_failure(self, data_key: str, original_data: Dict):
        """🔥 新增：处理数据存储失败"""
        try:
            self.logger.error(f"❌ 数据存储失败处理: {data_key}")
            
            # 记录失败统计
            if not hasattr(self, '_storage_failure_count'):
                self._storage_failure_count = {}
                
            self._storage_failure_count[data_key] = self._storage_failure_count.get(data_key, 0) + 1
            failure_count = self._storage_failure_count[data_key]
            
            # 如果失败次数过多，发出告警
            if failure_count >= 5:
                self.logger.error(f"🚨 数据存储持续失败: {data_key}, 失败次数={failure_count}")
                self.logger.error(f"🚨 这可能导致套利机会检测失败")
                
            # 记录原始数据用于调试
            self.logger.debug(f"🔍 失败的原始数据: {original_data}")
            
            # 尝试重新存储（简化版本）
            try:
                exchange = original_data.get('exchange', '').lower()
                symbol = original_data.get('symbol', '')
                market_type = original_data.get('market_type', 'spot')
                
                if exchange and symbol:
                    # 🔥 关键修复：确保统一时间戳处理器导入
                    from websocket.unified_timestamp_processor import get_synced_timestamp
                    
                    # 创建最小化的MarketData对象
                    minimal_data = MarketData(
                        exchange=exchange,
                        symbol=symbol,
                        price=original_data.get('price', 0.0),
                        timestamp=self.timestamp_processor.get_synced_timestamp(None),
                        orderbook={},
                        incomplete_orderbook=True,
                        incomplete_reason="存储失败后的恢复数据"
                    )
                    
                    # 尝试重新存储
                    self.market_data[data_key] = minimal_data
                    
                    if data_key in self.market_data:
                        self.logger.info(f"✅ 数据存储失败后恢复成功: {data_key}")
                    else:
                        self.logger.error(f"❌ 数据存储失败后恢复仍然失败: {data_key}")
                        
            except Exception as recovery_e:
                self.logger.error(f"❌ 数据存储失败恢复异常: {recovery_e}")
                
        except Exception as e:
            self.logger.error(f"❌ 数据存储失败处理异常: {e}")

    def _generate_standardized_data_key(self, exchange: str, market_type: str, symbol: str) -> str:
        """🔥 新增：生成标准化的数据键格式"""
        try:
            # 🔥 统一数据键格式：exchange_markettype_symbol
            # 确保所有交易所都使用相同的格式
            
            # 标准化交易所名称
            exchange_normalized = exchange.lower().strip()
            
            # 标准化市场类型
            market_type_normalized = market_type.lower().strip()
            
            # 标准化交易对格式
            symbol_normalized = symbol.upper().strip()
            
            # 🔥 验证数据键组件
            if not exchange_normalized or not market_type_normalized or not symbol_normalized:
                self.logger.warning(f"⚠️ 数据键组件无效: exchange={exchange_normalized}, market_type={market_type_normalized}, symbol={symbol_normalized}")
                return f"invalid_{int(time.time())}"
                
            # 生成标准化数据键
            data_key = f"{exchange_normalized}_{market_type_normalized}_{symbol_normalized}"
            
            # 🔥 验证数据键格式
            if not self._validate_data_key_format(data_key):
                self.logger.warning(f"⚠️ 生成的数据键格式无效: {data_key}")
                return f"invalid_{exchange_normalized}_{market_type_normalized}_{symbol_normalized}"
                
            return data_key
            
        except Exception as e:
            self.logger.error(f"❌ 数据键生成异常: {e}")
            # 返回应急数据键
            return f"emergency_{exchange}_{market_type}_{symbol}_{int(time.time())}"
            
    def _validate_data_key_format(self, data_key: str) -> bool:
        """🔥 新增：验证数据键格式"""
        try:
            # 检查基本格式：exchange_markettype_symbol
            parts = data_key.split('_')
            
            if len(parts) < 3:
                return False
                
            exchange, market_type, symbol = parts[0], parts[1], '_'.join(parts[2:])
            
            # 验证交易所名称
            valid_exchanges = ['gate', 'bybit', 'okx']
            if exchange not in valid_exchanges:
                return False
                
            # 验证市场类型
            valid_market_types = ['spot', 'futures', 'linear']
            if market_type not in valid_market_types:
                return False
                
            # 验证交易对格式
            if not symbol or len(symbol) < 3:
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 数据键格式验证异常: {e}")
            return False
            
    def _standardize_all_existing_data_keys(self):
        """🔥 新增：标准化所有现有数据键"""
        try:
            if not self.market_data:
                return
                
            # 收集需要重新生成键的数据
            data_to_migrate = []
            
            for old_key, market_data in list(self.market_data.items()):
                # 尝试解析旧键
                try:
                    parts = old_key.split('_')
                    if len(parts) >= 3:
                        exchange = parts[0]
                        market_type = parts[1]
                        symbol = '_'.join(parts[2:])
                        
                        # 生成新的标准化键
                        new_key = self._generate_standardized_data_key(exchange, market_type, symbol)
                        
                        if new_key != old_key:
                            data_to_migrate.append((old_key, new_key, market_data))
                            
                except Exception as e:
                    self.logger.warning(f"⚠️ 无法解析数据键: {old_key} - {e}")
                    
            # 执行数据迁移
            migrated_count = 0
            for old_key, new_key, market_data in data_to_migrate:
                try:
                    # 删除旧键
                    del self.market_data[old_key]
                    
                    # 添加新键
                    self.market_data[new_key] = market_data
                    
                    migrated_count += 1
                    self.logger.debug(f"🔄 数据键迁移: {old_key} → {new_key}")
                    
                except Exception as e:
                    self.logger.error(f"❌ 数据键迁移失败: {old_key} → {new_key} - {e}")
                    
            if migrated_count > 0:
                self.logger.info(f"✅ 数据键标准化完成: 迁移{migrated_count}个键")
                
        except Exception as e:
            self.logger.error(f"❌ 数据键标准化异常: {e}")

    def _measure_exchange_latency(self, exchange: str) -> float:
        """🔥 新增：测量交易所延迟的简单实现"""
        try:
            # 🔥 使用模拟延迟数据，基于交易所特性
            # 在实际环境中，这些数据会被真实的网络延迟替代
            exchange_latencies = {
                'gate': 45.0,    # Gate.io 通常延迟
                'bybit': 35.0,   # Bybit 通常延迟
                'okx': 40.0,     # OKX 通常延迟
            }

            base_latency = exchange_latencies.get(exchange.lower(), 50.0)

            # 🔥 添加小幅随机变化，模拟真实网络波动
            import random
            variation = random.uniform(-10.0, 15.0)  # ±10-15ms变化
            actual_latency = max(10.0, base_latency + variation)  # 最小10ms

            return actual_latency

        except Exception:
            return 50.0  # 默认50ms延迟

    def _init_combinations(self) -> List[CombinationConfig]:
        """初始化套利组合配置"""
        combinations = [
            CombinationConfig("gate_spot_bybit_futures", "gate", "bybit"),
            CombinationConfig("bybit_spot_gate_futures", "bybit", "gate"),
            CombinationConfig("okx_spot_bybit_futures", "okx", "bybit"),
            CombinationConfig("bybit_spot_okx_futures", "bybit", "okx"),
            CombinationConfig("okx_spot_gate_futures", "okx", "gate"),
            CombinationConfig("gate_spot_okx_futures", "gate", "okx"),
        ]

        self.logger.info(f"Initialized {len(combinations)} arbitrage combinations")
        return combinations

    async def initialize(self):
        """初始化扫描器 - 🔥 修复：延迟WebSocket回调注册，解决初始化顺序问题"""
        try:
            self.logger.info("Initializing OpportunityScanner...")

            # 🔥 关键修复：尝试注册WebSocket回调，但不因失败而阻止初始化
            try:
                await self._register_websocket_callbacks()
            except Exception as e:
                self.logger.warning(f"⚠️ WebSocket回调注册失败，但系统将继续运行: {e}")
                self.logger.info("💡 系统将在WebSocket管理器可用时自动重试")

            # 初始化WebSocket连接
            await self._init_websocket_connections()

            # 🔥 新增：启动数据存储健康检查
            await self._start_data_storage_health_check()

            # 🔥 新增：标准化现有数据键格式
            self._standardize_all_existing_data_keys()

            # 🔥 修复：检测可用组合，但不因为没有数据而失败
            try:
                await self._check_available_combinations()
            except Exception as e:
                self.logger.warning(f"⚠️ 初始组合检测失败（可能是数据未到达）: {e}")
                self.logger.info("💡 系统将在数据到达后自动激活组合")

            # 🔥 修复：无论是否有数据都设置为运行状态
            self.running = True

            # 启动数据更新任务
            asyncio.create_task(self._market_data_loop())

            self.logger.info("✅ OpportunityScanner initialized successfully")
            self.logger.info("📡 等待WebSocket数据到达以激活套利组合...")

        except Exception as e:
            self.logger.error(f"Failed to initialize OpportunityScanner: {e}")
            # 🔥 修复：不再抛出异常，允许系统继续运行
            self.logger.warning("⚠️ OpportunityScanner初始化部分失败，但系统将继续运行")
            self.running = True  # 确保设置为运行状态

    async def _start_data_storage_health_check(self):
        """🔥 新增：启动数据存储健康检查"""
        try:
            self.logger.info("🏥 启动数据存储健康检查...")
            
            # 初始化健康检查统计
            if not hasattr(self, '_health_check_stats'):
                self._health_check_stats = {
                    'total_data_keys': 0,
                    'bybit_futures_keys': 0,
                    'last_check_time': 0,
                    'check_interval': 30.0,  # 30秒检查一次
                    'alerts_sent': {}
                }
            
            # 启动后台健康检查任务
            asyncio.create_task(self._health_check_loop())
            
            self.logger.info("✅ 数据存储健康检查启动成功")
            
        except Exception as e:
            self.logger.error(f"❌ 数据存储健康检查启动失败: {e}")
            
    async def _health_check_loop(self):
        """🔥 新增：数据存储健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self._health_check_stats['check_interval'])
                
                if not self.running:
                    continue
                    
                await self._perform_health_check()
                
            except Exception as e:
                self.logger.error(f"❌ 健康检查循环异常: {e}")
                await asyncio.sleep(5)  # 异常后等待5秒
                
    async def _perform_health_check(self):
        """🔥 新增：执行数据存储健康检查"""
        try:
            current_time = time.time()
            
            # 统计当前数据状态
            total_keys = len(self.market_data)
            bybit_futures_keys = [key for key in self.market_data.keys() if 'bybit_futures' in key]
            bybit_futures_count = len(bybit_futures_keys)
            
            # 更新统计
            self._health_check_stats['total_data_keys'] = total_keys
            self._health_check_stats['bybit_futures_keys'] = bybit_futures_count
            self._health_check_stats['last_check_time'] = current_time
            
            # 检查Bybit期货数据健康状态
            if bybit_futures_count == 0:
                alert_key = "bybit_futures_missing"
                last_alert = self._health_check_stats['alerts_sent'].get(alert_key, 0)
                
                # 每5分钟发送一次告警
                if current_time - last_alert > 300:
                    self.logger.error("🚨 健康检查告警: Bybit期货数据键为0")
                    self.logger.error("🚨 这将导致套利机会检测失败")
                    self.logger.error(f"📊 当前数据状态: 总键数={total_keys}, Bybit期货键数={bybit_futures_count}")
                    
                    self._health_check_stats['alerts_sent'][alert_key] = current_time
                    
            elif bybit_futures_count > 0:
                # Bybit期货数据恢复正常
                alert_key = "bybit_futures_recovered"
                last_alert = self._health_check_stats['alerts_sent'].get(alert_key, 0)
                
                if current_time - last_alert > 300:
                    self.logger.info(f"✅ 健康检查: Bybit期货数据正常，键数={bybit_futures_count}")
                    self._health_check_stats['alerts_sent'][alert_key] = current_time
                    
            # 检查数据新鲜度
            stale_data_count = 0
            for key, market_data in self.market_data.items():
                # 🔥 关键修复：使用统一数据年龄计算函数，避免时间戳单位混用
                from websocket.unified_timestamp_processor import calculate_data_age
                data_age_seconds = calculate_data_age(market_data.timestamp, current_time)
                data_age = data_age_seconds * 1000  # 转换为毫秒
                if data_age > 10000:  # 10秒算过期
                    stale_data_count += 1
                    
            if stale_data_count > total_keys * 0.5:  # 超过50%数据过期
                alert_key = "stale_data_warning"
                last_alert = self._health_check_stats['alerts_sent'].get(alert_key, 0)
                
                if current_time - last_alert > 300:
                    self.logger.warning(f"⚠️ 健康检查: {stale_data_count}/{total_keys}个数据键过期")
                    self._health_check_stats['alerts_sent'][alert_key] = current_time
                    
            # 🔥 增强：详细的系统监控日志
            self._log_detailed_system_status(total_keys, bybit_futures_count, stale_data_count, current_time)
            
            # 记录健康检查日志（每10分钟一次）
            if current_time % 600 < 30:  # 大约每10分钟
                self.logger.info(f"🏥 数据存储健康检查: 总键数={total_keys}, Bybit期货键数={bybit_futures_count}, 过期数据={stale_data_count}")
                
                # 🔥 新增：生成详细的监控报告
                await self._generate_monitoring_report()
                
        except Exception as e:
            self.logger.error(f"❌ 健康检查执行异常: {e}")

    def _log_detailed_system_status(self, total_keys: int, bybit_futures_count: int, stale_data_count: int, current_time: float):
        """🔥 新增：记录详细的系统监控状态日志"""
        try:
            # 计算数据新鲜度百分比
            fresh_data_rate = ((total_keys - stale_data_count) / total_keys * 100) if total_keys > 0 else 0

            # 计算Bybit期货数据覆盖率
            bybit_coverage_rate = (bybit_futures_count / len(self.supported_symbols) * 100) if len(self.supported_symbols) > 0 else 0

            # 获取WebSocket连接状态
            websocket_connections = 0
            total_websockets = 0
            for exchange, ws_client in self.ws_clients.items():
                total_websockets += 1
                try:
                    if hasattr(ws_client, 'is_connected') and callable(ws_client.is_connected):
                        if ws_client.is_connected():
                            websocket_connections += 1
                except Exception:
                    pass

            websocket_health_rate = (websocket_connections / total_websockets * 100) if total_websockets > 0 else 0

            # 计算系统整体健康度
            overall_health = (fresh_data_rate * 0.4 + bybit_coverage_rate * 0.3 + websocket_health_rate * 0.3)

            # 确定健康状态等级
            if overall_health >= 90:
                health_status = "🟢 优秀"
            elif overall_health >= 70:
                health_status = "🟡 良好"
            elif overall_health >= 50:
                health_status = "🟠 警告"
            else:
                health_status = "🔴 严重"

            # 记录详细状态日志（每5分钟记录一次详细信息）
            if current_time % 300 < 30:  # 每5分钟
                self.logger.info(f"📊 系统监控详细状态:")
                self.logger.info(f"   🔍 数据状态: {total_keys}个数据源, {fresh_data_rate:.1f}%新鲜度")
                self.logger.info(f"   🎯 Bybit期货: {bybit_futures_count}个数据源, {bybit_coverage_rate:.1f}%覆盖率")
                self.logger.info(f"   🌐 WebSocket: {websocket_connections}/{total_websockets}个连接, {websocket_health_rate:.1f}%健康度")
                self.logger.info(f"   💯 整体健康度: {overall_health:.1f}% {health_status}")

                # 如果健康度低于70%，记录详细诊断信息
                if overall_health < 70:
                    self.logger.warning(f"⚠️ 系统健康度偏低，详细诊断:")
                    if fresh_data_rate < 80:
                        self.logger.warning(f"   📉 数据新鲜度不足: {stale_data_count}/{total_keys}个数据过期")
                    if bybit_coverage_rate < 80:
                        self.logger.warning(f"   📉 Bybit期货覆盖不足: {bybit_futures_count}/{len(self.supported_symbols)}个交易对")
                    if websocket_health_rate < 80:
                        self.logger.warning(f"   📉 WebSocket连接不稳定: {websocket_connections}/{total_websockets}个连接正常")

        except Exception as e:
            self.logger.error(f"❌ 详细系统状态日志记录失败: {e}")

    async def _generate_monitoring_report(self):
        """🔥 新增：生成详细的监控报告"""
        try:
            current_time = time.time()

            # 收集系统运行统计
            total_data_keys = len(self.market_data)
            running_time = current_time - getattr(self, '_start_time', current_time)

            # 收集交易所连接状态
            exchange_status = {}
            for exchange, ws_client in self.ws_clients.items():
                try:
                    is_connected = False
                    if hasattr(ws_client, 'is_connected') and callable(ws_client.is_connected):
                        is_connected = ws_client.is_connected()

                    # 统计该交易所的数据量
                    spot_data_count = len([k for k in self.market_data.keys() if k.startswith(f"{exchange}_spot")])
                    futures_data_count = len([k for k in self.market_data.keys() if k.startswith(f"{exchange}_futures")])

                    exchange_status[exchange] = {
                        'connected': is_connected,
                        'spot_data': spot_data_count,
                        'futures_data': futures_data_count,
                        'total_data': spot_data_count + futures_data_count
                    }
                except Exception as e:
                    exchange_status[exchange] = {
                        'connected': False,
                        'error': str(e),
                        'spot_data': 0,
                        'futures_data': 0,
                        'total_data': 0
                    }

            # 收集套利机会统计
            current_opportunities = len(getattr(self, 'current_opportunities', []))
            available_combinations = len(getattr(self, 'available_combinations', []))
            total_combinations = len(getattr(self, 'combinations', []))

            # 收集错误统计
            error_stats = getattr(self, '_error_stats', {})
            total_errors = error_stats.get('total_errors', 0)
            recent_errors = len(error_stats.get('error_rate_window', []))

            # 生成监控报告
            self.logger.info(f"📋 OpportunityScanner监控报告 (运行时间: {running_time/3600:.1f}小时)")
            self.logger.info(f"   📊 数据状态: {total_data_keys}个数据源")
            self.logger.info(f"   🎯 套利机会: {current_opportunities}个当前机会, {available_combinations}/{total_combinations}个可用组合")
            self.logger.info(f"   ❌ 错误统计: 总计{total_errors}个, 最近5分钟{recent_errors}个")

            # 详细交易所状态
            for exchange, status in exchange_status.items():
                connection_icon = "✅" if status['connected'] else "❌"
                self.logger.info(f"   {connection_icon} {exchange.upper()}: 现货{status['spot_data']}个, 期货{status['futures_data']}个数据源")

            # 如果有错误，记录错误类型分布
            if total_errors > 0:
                error_types = error_stats.get('error_types', {})
                if error_types:
                    self.logger.info(f"   🔍 错误类型分布: {error_types}")

        except Exception as e:
            self.logger.error(f"❌ 监控报告生成失败: {e}")

    def _record_data_processing_error(self, error_type: str, error_message: str, data: Dict):
        """🔥 新增：记录数据处理错误统计"""
        try:
            # 初始化错误统计
            if not hasattr(self, '_error_stats'):
                self._error_stats = {
                    'total_errors': 0,
                    'error_types': {},
                    'last_error_time': 0,
                    'error_rate_window': []
                }
            
            current_time = time.time()
            
            # 更新错误统计
            self._error_stats['total_errors'] += 1
            self._error_stats['error_types'][error_type] = self._error_stats['error_types'].get(error_type, 0) + 1
            self._error_stats['last_error_time'] = current_time
            
            # 维护错误率窗口（最近5分钟）
            self._error_stats['error_rate_window'].append(current_time)
            # 清理5分钟前的错误记录
            cutoff_time = current_time - 300  # 5分钟
            self._error_stats['error_rate_window'] = [
                t for t in self._error_stats['error_rate_window'] if t > cutoff_time
            ]
            
            # 检查错误率是否过高
            recent_errors = len(self._error_stats['error_rate_window'])
            if recent_errors >= 50:  # 5分钟内超过50个错误
                self.logger.error(f"🚨 数据处理错误率过高: {recent_errors}个错误/5分钟")
                self.logger.error(f"🚨 错误类型分布: {self._error_stats['error_types']}")
                
            # 记录详细错误信息（每10个错误记录一次详情）
            if self._error_stats['total_errors'] % 10 == 0:
                self.logger.warning(f"⚠️ 数据处理错误统计: 总计{self._error_stats['total_errors']}个")
                self.logger.warning(f"   错误类型: {self._error_stats['error_types']}")
                self.logger.warning(f"   最近5分钟: {recent_errors}个错误")
                
        except Exception as e:
            self.logger.error(f"❌ 错误统计记录失败: {e}")
            
    async def _attempt_error_recovery(self, data: Dict, error_message: str):
        """🔥 新增：尝试错误恢复"""
        try:
            self.logger.info(f"🔧 尝试数据处理错误恢复...")
            
            # 检查是否是可恢复的错误
            recoverable_errors = [
                "数据验证失败",
                "价格合理性验证失败", 
                "数据键生成失败",
                "数据存储失败"
            ]
            
            is_recoverable = any(err in error_message for err in recoverable_errors)
            
            if not is_recoverable:
                self.logger.debug(f"🔍 错误不可恢复: {error_message}")
                return False
                
            # 尝试简化数据处理
            exchange = data.get('exchange', '').lower()
            symbol = data.get('symbol', '')
            market_type = data.get('market_type', 'spot')
            price = data.get('price', 0.0)
            
            if not exchange or not symbol:
                self.logger.debug(f"🔍 数据缺少基本字段，无法恢复")
                return False
                
            # 创建最小化的数据键
            try:
                simple_data_key = f"{exchange}_{market_type}_{symbol.upper()}"
                
                # 创建简化的MarketData对象
                recovery_data = MarketData(
                    exchange=exchange,
                    symbol=symbol.upper(),
                    price=max(0.0, price),  # 确保价格非负
                    timestamp=self.timestamp_processor.get_synced_timestamp(None),
                    orderbook={},
                    incomplete_orderbook=True,
                    incomplete_reason=f"错误恢复数据: {error_message[:50]}"
                )
                
                # 尝试存储恢复数据
                with self.data_lock:
                    self.market_data[simple_data_key] = recovery_data
                    
                # 验证恢复是否成功
                if simple_data_key in self.market_data:
                    self.logger.info(f"✅ 错误恢复成功: {simple_data_key}")
                    
                    # 特别记录Bybit期货恢复
                    if exchange == "bybit" and market_type == "futures":
                        self.logger.info(f"🎯 Bybit期货数据错误恢复成功: {simple_data_key}")
                        
                    return True
                else:
                    self.logger.error(f"❌ 错误恢复失败: 数据未成功存储")
                    return False
                    
            except Exception as recovery_e:
                self.logger.error(f"❌ 错误恢复过程异常: {recovery_e}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 错误恢复机制异常: {e}")
            return False

    async def _register_websocket_callbacks(self):
        """🔥 强化：注册WebSocket回调 - 专门处理orderbook数据"""
        try:
            self.logger.info("🔗 注册WebSocket orderbook回调...")

            # 获取WebSocket管理器
            from websocket.ws_manager import get_ws_manager
            ws_manager = get_ws_manager()

            if ws_manager:
                # 🔥 强化：注册专门的orderbook数据回调
                ws_manager.register_callback("market_data", self._on_orderbook_data)
                self.logger.info("✅ 已注册WebSocket orderbook数据回调")

                # 🔥 强化：验证回调类型，确保无ticker残留
                callback_types = list(ws_manager.callbacks.keys())
                if "ticker" in callback_types:
                    self.logger.error("❌ 发现ticker回调类型残留，需要清理")
                else:
                    self.logger.info("✅ 确认无ticker回调类型，系统已完全使用orderbook")

                # 验证回调注册成功
                market_data_callbacks = ws_manager.callbacks.get("market_data", [])
                self.logger.info(f"📊 当前orderbook回调数量: {len(market_data_callbacks)}")

                # 检查我们的回调是否在列表中
                callback_registered = any(
                    hasattr(callback, '__self__') and callback.__self__ is self
                    for callback in market_data_callbacks
                )

                if callback_registered:
                    self.logger.info("✅ OpportunityScanner orderbook回调注册验证成功")
                    return True  # 注册成功
                else:
                    self.logger.error("❌ OpportunityScanner orderbook回调注册验证失败")
                    return False

            else:
                self.logger.warning("⚠️ 无法获取WebSocket管理器，回调注册失败")
                self.logger.info("💡 系统将继续运行，WebSocket管理器可用时可手动重试注册")
                return False

        except Exception as e:
            self.logger.warning(f"⚠️ WebSocket orderbook回调注册失败: {e}")
            self.logger.info("💡 系统将继续运行，可稍后重试注册")
            return False



    def _import_websocket_modules(self):
        """🔥 修复：动态导入WebSocket模块，避免循环依赖"""
        try:
            # 动态导入WebSocket客户端类
            from websocket.ws_client import WebSocketClient
            from websocket.gate_ws import GateWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient
            from websocket.okx_ws import OKXWebSocketClient

            return {
                'WebSocketClient': WebSocketClient,
                'GateWebSocketClient': GateWebSocketClient,
                'BybitWebSocketClient': BybitWebSocketClient,
                'OKXWebSocketClient': OKXWebSocketClient
            }
        except ImportError as e:
            self.logger.warning(f"WebSocket模块导入失败: {e}")
            return {}

    async def _init_websocket_connections(self):
        """🔥 删除重复WebSocket初始化：OpportunityScanner不再管理WebSocket"""
        try:
            self.logger.info("🔥 OpportunityScanner跳过WebSocket初始化（由main.py统一管理）")

            # 🔥 删除重复WebSocket初始化：WebSocket连接由main.py统一管理
            # OpportunityScanner只负责接收数据并更新价格，不管理连接

            # 🎯 初始化空的WebSocket客户端字典 - 为兼容性保留
            self.ws_clients = {}

            self.logger.info("✅ OpportunityScanner WebSocket初始化跳过（避免重复）")
            self.logger.info("📡 等待main.py提供WebSocket数据...")

        except Exception as e:
            self.logger.error(f"❌ OpportunityScanner WebSocket初始化失败: {e}")
            # 不抛出异常，允许系统在没有WebSocket的情况下运行

    async def _subscribe_all_data(self):
        """订阅所有WebSocket数据 - 🔥 简化：数据订阅由外部系统管理"""
        try:
            # 🔥 OpportunityScanner不再直接订阅WebSocket数据
            # 数据通过update_price方法或_on_market_data回调接收
            
            self.logger.info("📊 OpportunityScanner准备接收WebSocket数据")
            self.logger.info(f"🎯 支持的交易对: {', '.join(self.supported_symbols[:3])}...共{len(self.supported_symbols)}个")
            
            # 🔥 初始化价格日志限制器
            if not hasattr(self, '_last_price_log'):
                self._last_price_log = {}
                self.logger.warning("🔥 动态初始化_last_price_log属性，修复OKX数据处理问题")

        except Exception as e:
            self.logger.error(f"❌ WebSocket数据订阅设置失败: {e}")

    # 🔥 系统只使用OrderBook数据



    async def _on_orderbook_data(self, data: Dict):
        """🔥 强化：专门处理orderbook数据更新"""
        try:
            # 🔥 强化：严格验证数据类型，确保是orderbook数据
            data_type = data.get('data_type', '')
            has_orderbook = 'asks' in data and 'bids' in data

            if data_type == 'ticker':
                self.logger.warning(f"⚠️ 收到ticker数据，应该已被过滤: {data.get('symbol', 'unknown')}")
                return

            if not has_orderbook:
                self.logger.warning(f"⚠️ 收到非orderbook数据: {data.get('symbol', 'unknown')}, data_type={data_type}")
                return

            exchange = str(data.get('exchange', '')).lower()
            symbol = data.get('symbol', '')
            market_type = data.get('market_type', 'spot')

            # 🔥 强化：验证orderbook数据质量
            asks = data.get('asks', [])
            bids = data.get('bids', [])

            if len(asks) < 5 or len(bids) < 5:
                self.logger.warning(f"⚠️ {exchange}_{symbol} orderbook深度不足: asks={len(asks)}, bids={len(bids)}")
                return

            # 🔥 修复：移除中间价计算，直接使用最优价格用于日志
            best_ask = float(asks[0][0]) if asks else 0
            best_bid = float(bids[0][0]) if bids else 0

            # 🔥 强化：详细的orderbook数据接收日志（移除中间价）
            if exchange == "bybit" and market_type == "futures":
                self.logger.info(f"📊 OpportunityScanner接收Bybit期货orderbook: {symbol}, 最优买价={best_bid:.6f}, 最优卖价={best_ask:.6f}")
                if best_bid > 0:
                    self.logger.info(f"🔍 orderbook详情: asks={len(asks)}档, bids={len(bids)}档, 最优价差={(best_ask-best_bid)/best_bid*10000:.2f}bps")

            # 🔥 强化：调用原有的市场数据处理逻辑
            await self._on_market_data(data)

        except Exception as e:
            self.logger.error(f"❌ 处理orderbook数据异常: {e}")

    async def _on_market_data(self, data: Dict):
        """处理市场数据更新 - � 强化：专门处理已验证的orderbook数据"""
        try:
            exchange = str(data.get('exchange', '')).lower()
            symbol = data.get('symbol', '')
            market_type = data.get('market_type', 'spot')

            # 🔥 修复：移除中间价计算，直接使用订单簿最优价格
            asks = data.get('asks', [])
            bids = data.get('bids', [])

            # 🔥 关键修复：不再计算中间价，直接使用最优买价作为参考价格
            # 这样避免了中间价导致的差价计算不准确问题
            if asks and bids:
                best_ask = float(asks[0][0])
                best_bid = float(bids[0][0])
                # 使用最优买价作为参考价格（更接近实际交易价格）
                price = best_bid
            else:
                price = data.get('price', 0.0)  # 兼容性回退

            # 🔥 处理price字段的多种格式
            if price <= 0:
                # 尝试从不同字段获取价格
                price = data.get('last', data.get('lastPrice', data.get('close', 0.0)))

            # 🔥 关键修复：确保统一时间戳处理器在顶部导入
            from websocket.unified_timestamp_processor import get_synced_timestamp

            # 🔥 关键修复：确保_last_price_log属性存在
            if not hasattr(self, '_last_price_log'):
                self._last_price_log = {}
                self.logger.warning("🔥 动态初始化_last_price_log属性，修复OKX数据处理问题")

            # 🔥 标准化symbol格式为统一格式（如ADA-USDT）
            standardized_symbol = symbol
            if exchange == 'gate' and '_' in symbol:
                # Gate: ADA_USDT -> ADA-USDT
                standardized_symbol = symbol.replace('_', '-')
            elif exchange == 'bybit' and '-' not in symbol and '_' not in symbol:
                # Bybit: ADAUSDT -> ADA-USDT
                if symbol.endswith('USDT') and len(symbol) > 4:
                    base = symbol[:-4]
                    standardized_symbol = f"{base}-USDT"
            else:
                # 🔥 统一处理：OKX和其他交易所使用相同逻辑
                if '-SWAP' in symbol:
                    # 期货格式: ADA-USDT-SWAP -> ADA-USDT
                    standardized_symbol = symbol.replace('-SWAP', '')
                elif '-' in symbol:
                    # 标准格式: ADA-USDT -> ADA-USDT (保持不变)
                    standardized_symbol = symbol

            # 🔥 修复：检查标准化symbol是否在支持的列表中
            self.logger.debug(f"🔍 条件检查: exchange={bool(exchange)}, symbol_in_supported={standardized_symbol in self.supported_symbols}, price_valid={price > 0}")
            if exchange and standardized_symbol in self.supported_symbols and price > 0:
                # 🔥 新增：价格合理性验证
                if not self._validate_price_reasonableness(standardized_symbol, exchange, market_type, price):
                    self.logger.warning(f"⚠️ 价格合理性验证失败，跳过更新: {exchange}_{market_type}_{standardized_symbol} = ${price:.6f}")
                    return
                self.logger.debug(f"🔍 进入主处理逻辑: {exchange}_{market_type}_{standardized_symbol}")
                with self.data_lock:
                    # 🎯 关键修复：使用统一的数据键生成方法
                    data_key = self._generate_standardized_data_key(exchange, market_type, standardized_symbol)

                    # 🔥 关键修复：正确处理WebSocket订单簿数据
                    # 确保asks和bids数据正确传递到orderbook字段
                    orderbook_data = {}

                    # 🔥 关键修复：统一订单簿数据处理逻辑，严格验证数据完整性
                    if 'asks' in data and 'bids' in data:
                        self.logger.debug(f"🔍 进入订单簿处理逻辑: {exchange}_{market_type}_{standardized_symbol}")
                        # 🚨 严格验证：订单簿数据完整性检查
                        asks = data.get('asks', [])
                        bids = data.get('bids', [])

                        self.logger.debug(f"🔍 订单簿数据检查: asks={len(asks) if asks else 0}, bids={len(bids) if bids else 0}")
                        self.logger.debug(f"🔍 数据完整性检查: not asks={not asks}, not bids={not bids}")

                        # 🔥 修复：使用统一日志系统记录订单簿数据
                        # 不再导入不存在的模块，使用现有的统一日志系统

                        # 🔥 关键修复：即使数据不完整，也要创建orderbook_data，避免空字典
                        # 这样ExecutionEngine可以获取到部分数据，而不是完全失败
                        if not asks or not bids:
                            # 🔥 修复：使用统一日志系统记录不完整的订单簿数据
                            reason = f"asks={len(asks) if asks else 0}, bids={len(bids) if bids else 0}"
                            self.logger.warning(f"⚠️ WebSocket订单簿数据缺失: {exchange}_{market_type}_{standardized_symbol} - {reason}")

                            # 🔥 修复：创建30档深度部分数据的orderbook，与UnifiedOrderSpreadCalculator保持一致
                            # 🔥 使用统一时间戳处理器确保一致性
                            fallback_timestamp = self.timestamp_processor.get_synced_timestamp(None)
                            
                            orderbook_data = {
                                'asks': asks[:30] if asks else [],  # 🔥 修复：升级为30档深度
                                'bids': bids[:30] if bids else [],  # 🔥 修复：升级为30档深度
                                'timestamp': data.get('timestamp', fallback_timestamp),
                                'symbol': standardized_symbol,
                                'exchange': exchange
                            }

                            self.logger.warning(f"⚠️ 创建部分订单簿数据: {exchange}_{market_type}_{standardized_symbol} - {reason}")

                            # 🔥 修复：检查WebSocket管理器是否已标记数据不完整
                            if data.get('incomplete_orderbook', False):
                                self.logger.debug(f"📊 WebSocket管理器已标记数据不完整，继续处理部分数据")
                            else:
                                # 标记为不完整数据
                                self.logger.warning(f"⚠️ 标记为不完整数据: {exchange}_{market_type}_{standardized_symbol}")
                        else:
                            # 🔥 修复：使用统一日志系统记录完整的订单簿数据
                            self.logger.debug(f"📖 WebSocket订单簿数据完整: {exchange}_{market_type}_{standardized_symbol} 卖单{len(asks)}档, 买单{len(bids)}档")

                            # 🔥 修复：创建30档深度订单簿数据，与UnifiedOrderSpreadCalculator保持一致
                            orderbook_data = {
                                'asks': asks[:30],  # 🔥 修复：升级为30档深度
                                'bids': bids[:30],  # 🔥 修复：升级为30档深度
                                'timestamp': data.get('timestamp', self.timestamp_processor.get_synced_timestamp(None)),
                                'symbol': standardized_symbol,
                                'exchange': exchange
                            }
                            # 🔥 调试日志：记录成功接收的订单簿数据
                            self.logger.debug(f"📊 接收完整订单簿数据: {exchange}_{market_type}_{standardized_symbol}, asks:{len(asks)}, bids:{len(bids)}")

                    elif 'orderbook' in data and isinstance(data['orderbook'], dict):
                        # 从嵌套的orderbook字段获取数据
                        nested_orderbook = data['orderbook']
                        if 'asks' in nested_orderbook and 'bids' in nested_orderbook:
                            nested_asks = nested_orderbook.get('asks', [])
                            nested_bids = nested_orderbook.get('bids', [])

                            # 🔥 修复：同样记录嵌套订单簿数据到专用日志
                            if not nested_asks or not nested_bids:
                                # 🔥 修复：使用统一日志系统记录不完整的嵌套订单簿数据
                                reason = f"nested_asks={len(nested_asks) if nested_asks else 0}, nested_bids={len(nested_bids) if nested_bids else 0}"
                                self.logger.warning(f"⚠️ WebSocket嵌套订单簿数据缺失: {exchange}_{market_type}_{standardized_symbol} - {reason}")

                                # 🔥 关键修复：创建部分嵌套数据的orderbook，而不是跳过
                                orderbook_data = {
                                    'asks': nested_asks[:10] if nested_asks else [],
                                    'bids': nested_bids[:10] if nested_bids else [],
                                    'timestamp': nested_orderbook.get('timestamp', self.timestamp_processor.get_synced_timestamp(None)),
                                    'symbol': standardized_symbol,
                                    'exchange': exchange
                                }

                                self.logger.warning(f"⚠️ 创建部分嵌套订单簿数据: {exchange}_{market_type}_{standardized_symbol} - {reason}")
                            else:
                                # 🔥 修复：使用统一日志系统记录完整的嵌套订单簿数据
                                self.logger.debug(f"📖 WebSocket嵌套订单簿数据完整: {exchange}_{market_type}_{standardized_symbol} 卖单{len(nested_asks)}档, 买单{len(nested_bids)}档")

                                orderbook_data = {
                                    'asks': nested_asks[:10],
                                    'bids': nested_bids[:10],
                                    'timestamp': nested_orderbook.get('timestamp', self.timestamp_processor.get_synced_timestamp(None)),
                                    'symbol': standardized_symbol,
                                    'exchange': exchange
                                }
                    else:
                        # 🔥 精准修复：纯价格数据不应该覆盖现有订单簿
                        if 'price' in data and data.get('price', 0) > 0:
                            # 检查是否已有订单簿数据
                            existing_data = self.market_data.get(data_key)
                            if existing_data and existing_data.orderbook and (existing_data.orderbook.get('asks') or existing_data.orderbook.get('bids')):
                                # 保留现有订单簿，只更新价格
                                orderbook_data = existing_data.orderbook
                                self.logger.debug(f"📊 保留现有订单簿（纯价格更新）: {exchange}_{market_type}_{standardized_symbol}")
                            else:
                                # 没有现有订单簿，创建空结构
                                orderbook_data = {
                                    'asks': [],
                                    'bids': [],
                                    'timestamp': data.get('timestamp', self.timestamp_processor.get_synced_timestamp(None)),
                                    'symbol': standardized_symbol,
                                    'exchange': exchange
                                }
                                self.logger.debug(f"📊 创建空订单簿结构（首次价格数据）: {exchange}_{market_type}_{standardized_symbol}")
                        else:
                            # 完全没有有效数据，保持空字典但记录日志
                            orderbook_data = {}
                            self.logger.debug(f"⚠️ 无有效订单簿或价格数据: {exchange}_{market_type}_{standardized_symbol}")
                    
                    # 🔥 修复：使用统一时间戳处理器，避免重复的时间戳转换逻辑
                    data_timestamp = self.timestamp_processor.get_synced_timestamp(data)

                    # 🔥 修复：检查数据完整性标记，从WebSocket管理器传递的标记
                    incomplete_orderbook = data.get('incomplete_orderbook', False)
                    incomplete_reason = data.get('incomplete_reason', '')

                    market_data = MarketData(
                        exchange=exchange,
                        symbol=standardized_symbol,
                        price=price,
                        timestamp=data_timestamp,  # 🔥 修复：使用统一的毫秒时间戳
                        orderbook=orderbook_data,  # 🔥 修复：包含完整订单簿数据
                        # ticker字段已移除
                        # 🔥 修复：正确设置数据完整性标记，解决ExecutionEngine中"unknown"状态问题
                        incomplete_orderbook=incomplete_orderbook,
                        incomplete_reason=incomplete_reason
                    )
                    self.market_data[data_key] = market_data

                    # 🔥 关键修复：验证Bybit期货数据存储成功
                    if exchange == "bybit" and market_type == "futures":
                        # 验证数据是否成功存储
                        if data_key in self.market_data:
                            self.logger.info(f"✅ Bybit期货数据存储成功: {data_key}")
                            self.logger.info(f"📊 当前market_data总数: {len(self.market_data)}")
                            
                            # 统计Bybit期货数据键
                            bybit_futures_keys = [key for key in self.market_data.keys() if 'bybit_futures' in key]
                            self.logger.info(f"🎯 Bybit期货数据键数量: {len(bybit_futures_keys)}")
                            
                            # 🔥 新增：数据质量验证
                            stored_data = self.market_data[data_key]
                            self._validate_stored_data_quality(data_key, stored_data)
                        else:
                            self.logger.error(f"❌ Bybit期货数据存储失败: {data_key}")
                            # 🔥 新增：存储失败告警
                            self._handle_data_storage_failure(data_key, data)

                    # 🔥 根源修复：删除重复的日志调用，统一使用套利机会检测中的日志记录

                    # 🔥 减少日志输出，避免刷屏 - 每个交易对每30秒最多1条日志
                    log_key = f"{data_key}_price_update"
                    current_time = time.time()

                    if log_key not in self._last_price_log or current_time - self._last_price_log[log_key] > 30:
                        self.logger.debug(f"📊 更新市场数据: {data_key} = ${price:.4f}")
                        self._last_price_log[log_key] = current_time

                    # 🔥 特殊处理：如果是Bybit数据，记录额外日志用于调试
                    if exchange == 'bybit':
                        bybit_log_key = f"bybit_data_received_{market_type}"
                        if bybit_log_key not in self._last_price_log or current_time - self._last_price_log[bybit_log_key] > 60:
                            self.logger.info(f"🎯 Bybit数据正常接收: {market_type} {standardized_symbol} = ${price:.4f}")
                            self._last_price_log[bybit_log_key] = current_time

            else:
                # 调试信息：记录无效数据
                if exchange and price <= 0:
                    self.logger.debug(f"⚠️ 收到无效价格数据: {exchange} {market_type} {symbol} price={price}")
                elif exchange and standardized_symbol not in self.supported_symbols:
                    # 只记录一次未支持的交易对
                    unsupported_key = f"unsupported_{standardized_symbol}"
                    if unsupported_key not in self._last_price_log:
                        self.logger.debug(f"ℹ️ 收到未支持的交易对: {standardized_symbol} (exchange={exchange})")
                        self._last_price_log[unsupported_key] = time.time()

        except ValueError as e:
            # 🔥 关键修复：ValueError是数据验证错误，记录但不中断系统运行
            self.logger.warning(f"⚠️ 数据验证失败，跳过此次更新: {e}")
            self.logger.debug(f"问题数据: {data}")
            # 🔥 新增：记录数据验证失败统计
            self._record_data_processing_error("validation_error", str(e), data)
            # 不重新抛出异常，避免中断WebSocket数据流
        except Exception as e:
            self.logger.error(f"❌ 处理市场数据时出错: {e}")
            self.logger.debug(f"问题数据: {data}")  # 调试信息
            # 🔥 增强：使用统一的错误处理机制
            self._record_data_processing_error("processing_error", str(e), data)
            
            # 🔥 新增：尝试错误恢复
            try:
                await self._attempt_error_recovery(data, str(e))
            except Exception as recovery_e:
                self.logger.error(f"❌ 错误恢复失败: {recovery_e}")

    async def _check_available_combinations(self):
        """检测可用组合 - 🔥 真正的智能检测：基于WebSocket连接状态和实际交易对存在性验证"""
        try:
            self.available_combinations.clear()
            
            # 🔥 第一步：动态检测每个交易所+市场类型的数据连接状态
            self.logger.info("🔍 开始智能检测交易所数据连接状态...")
            
            # 🚀 智能检测策略：
            # 1. 检查是否有来自该交易所+市场类型的数据
            # 2. 检查数据的新鲜度和完整性
            # 3. 自动标记不可用的数据源
            
            exchange_data_status = {
                'gate': {'spot': {'data_count': 0, 'last_update': 0}, 'futures': {'data_count': 0, 'last_update': 0}},
                'bybit': {'spot': {'data_count': 0, 'last_update': 0}, 'futures': {'data_count': 0, 'last_update': 0}},
                'okx': {'spot': {'data_count': 0, 'last_update': 0}, 'futures': {'data_count': 0, 'last_update': 0}}
            }
            
            current_time = time.time()
            
            # 🔥 关键修复：确保total_data_keys在方法开始就被定义
            total_data_keys = len(self.market_data)
            
            # 🔥 第二步：统计每个数据源的实际数据量和活跃度
            for data_key, market_data in self.market_data.items():
                try:
                    parts = data_key.split('_')
                    if len(parts) >= 3:
                        exchange = parts[0]
                        market_type = parts[1]
                        
                        if exchange in exchange_data_status and market_type in exchange_data_status[exchange]:
                            # 统计数据量
                            exchange_data_status[exchange][market_type]['data_count'] += 1
                            
                            # 🔥 关键修复：使用统一数据年龄计算函数，解决时间戳单位不一致问题
                            from websocket.unified_timestamp_processor import calculate_data_age
                            data_age = calculate_data_age(market_data.timestamp, current_time)
                            if data_age < 1:  # 🔥 修复：1秒内算活跃，与严格数据新鲜度要求保持一致
                                # 🔥 修复：统一时间戳单位 - market_data.timestamp现在是毫秒级整数
                                timestamp_seconds = market_data.timestamp / 1000
                                exchange_data_status[exchange][market_type]['last_update'] = max(
                                    exchange_data_status[exchange][market_type]['last_update'],
                                    timestamp_seconds  # 🔥 修复：使用统一的秒级时间戳存储
                                )
                except Exception as e:
                    self.logger.debug(f"解析数据key失败: {data_key} - {e}")
            
            # 🔥 第三步：基于实际数据状态判断数据源可用性
            available_data_sources = {}
            
            for exchange in ['gate', 'bybit', 'okx']:
                available_data_sources[exchange] = {}
                
                for market_type in ['spot', 'futures']:
                    status = exchange_data_status[exchange][market_type]
                    data_count = status['data_count']
                    last_update = status['last_update']
                    
                    # 🎯 🔥 修复：统一三交易所处理逻辑，删除OKX特殊化处理
                    # 所有交易所使用相同的判断标准，确保一致性
                    
                    is_available = (data_count > 0)  # 🚀 统一条件：只要有数据就可用
                    
                    # 🔥 统一三交易所处理：Gate/Bybit/OKX使用相同的可用性判断逻辑
                    # 确保三交易所完全一致的组合生成机制
                    
                    available_data_sources[exchange][market_type] = is_available
                    
                    status_icon = "✅" if is_available else "❌"
                    data_age = current_time - last_update if last_update > 0 else 999
                    
                    if is_available:
                        self.logger.info(f"   {status_icon} {exchange.upper()}-{market_type}: "
                                       f"{data_count}个数据源, 最新数据{data_age:.1f}秒前")
                    else:
                        self.logger.warning(f"   {status_icon} {exchange.upper()}-{market_type}: 无数据")
            
            # 🔥 第四步：基于数据源可用性智能生成可用组合
            all_combinations = [
                ("gate", "spot", "bybit", "futures", "gate_spot_bybit_futures"),
                ("bybit", "spot", "gate", "futures", "bybit_spot_gate_futures"),
                ("okx", "spot", "bybit", "futures", "okx_spot_bybit_futures"),
                ("bybit", "spot", "okx", "futures", "bybit_spot_okx_futures"),
                ("okx", "spot", "gate", "futures", "okx_spot_gate_futures"),
                ("gate", "spot", "okx", "futures", "gate_spot_okx_futures")
            ]
            
            self.logger.info("🎯 智能组合检测结果:")
            
            for spot_ex, spot_type, futures_ex, futures_type, combination_name in all_combinations:
                spot_available = available_data_sources[spot_ex][spot_type]
                futures_available = available_data_sources[futures_ex][futures_type]
                
                if spot_available and futures_available:
                    # 🚀 关键修复：进一步检查是否有共同的交易对数据，并验证交易对实际存在性
                    common_symbols_count, valid_symbols = await self._count_common_symbol_data_with_verification(spot_ex, spot_type, futures_ex, futures_type)
                    
                    if common_symbols_count > 0:
                        # 🔥 修复：找到对应的CombinationConfig对象并添加到可用组合
                        for combo_config in self.combinations:
                            if combo_config.name == combination_name:
                                self.available_combinations.append(combo_config)
                                break
                        self.logger.info(f"   ✅ {combination_name}: {common_symbols_count}个有效交易对 [{', '.join(list(valid_symbols)[:3])}{'...' if len(valid_symbols) > 3 else ''}]")
                    else:
                        self.logger.warning(f"   ⚠️ {combination_name}: 数据源可用但无有效交易对")
                else:
                    missing_sources = []
                    if not spot_available:
                        missing_sources.append(f"{spot_ex}-{spot_type}")
                    if not futures_available:
                        missing_sources.append(f"{futures_ex}-{futures_type}")
                    
                    self.logger.warning(f"   ❌ {combination_name}: 缺少数据源 [{', '.join(missing_sources)}]")
            
            # 🔥 第五步：结果汇总和自适应策略
            total_combinations = len(all_combinations)
            available_count = len(self.available_combinations)
            availability_rate = available_count / total_combinations * 100
            
            self.logger.info(f"📊 智能组合检测完成:")
            self.logger.info(f"   ✅ 可用组合: {available_count}/{total_combinations} ({availability_rate:.1f}%)")
            
            if available_count > 0:
                self.logger.info(f"🎉 系统正常运行 - 智能适应{available_count}个可用组合:")
                for combo in self.available_combinations:
                    self.logger.info(f"      ✅ {combo.name}")
            else:
                # 🔥 关键修复：现在total_data_keys已经在方法开始时定义
                if total_data_keys == 0:
                    self.logger.warning(f"⚠️ 暂无市场数据 - WebSocket可能仍在连接中，系统将继续尝试")
                elif total_data_keys < 6:  # 少于6个数据源（每个交易所2个：现货+期货）
                    self.logger.warning(f"⚠️ 市场数据不完整({total_data_keys}个数据源) - 部分交易所可能连接延迟，系统将继续尝试")
                else:
                    self.logger.warning(f"⚠️ 暂无满足条件的套利组合 - 所有交易所已连接但价差不足，系统正常运行")
                
                # 🔥 重要：不再使用ERROR级别，因为这是正常的市场状态
                # 系统应该在没有立即可用组合时仍然继续运行
                
            # 🔥 第六步：动态适应性提示
            if availability_rate < 50:
                if total_data_keys < 6:
                    self.logger.info(f"💡 提示: 等待更多交易所连接完成({total_data_keys}/6个数据源已就绪)")
                else:
                    self.logger.warning(f"⚠️ 可用组合较少({availability_rate:.1f}%)，建议检查网络连接或调整套利阈值")
            elif availability_rate >= 80:
                self.logger.info(f"🚀 组合可用性优秀({availability_rate:.1f}%)，系统运行最佳状态")
                
        except Exception as e:
            self.logger.error(f"❌ 智能组合检测异常: {e}")
            import traceback
            traceback.print_exc()
    
    async def _count_common_symbol_data_with_verification(self, spot_ex: str, spot_type: str, futures_ex: str, futures_type: str) -> Tuple[int, Set[str]]:
        """统计两个数据源的共同交易对数量，并验证交易对实际存在性 - 🔥 核心修复"""
        try:
            spot_symbols = set()
            futures_symbols = set()

            # 从实际市场数据中提取交易对
            for data_key in self.market_data.keys():
                parts = data_key.split('_')
                if len(parts) >= 3:
                    exchange = parts[0]
                    market_type = parts[1]
                    symbol = '_'.join(parts[2:])  # 处理可能包含下划线的交易对

                    if exchange == spot_ex and market_type == spot_type:
                        spot_symbols.add(symbol)
                    elif exchange == futures_ex and market_type == futures_type:
                        futures_symbols.add(symbol)

            # 计算交集 - 这些是WebSocket数据中的共同交易对
            common_symbols_from_data = spot_symbols & futures_symbols

            if not common_symbols_from_data:
                self.logger.debug(f"数据源比较: {spot_ex}-{spot_type}({len(spot_symbols)}) "
                                f"∩ {futures_ex}-{futures_type}({len(futures_symbols)}) = 0（无共同交易对）")
                return 0, set()

            # 🔥 关键修复：简化验证 - 有WebSocket数据就认为可用，避免过度API验证
            # 如果WebSocket能接收到数据，说明交易对是存在的
            verified_count = len(common_symbols_from_data)
            verified_symbols = common_symbols_from_data

            if verified_count > 0:
                self.logger.info(f"✅ 简化验证完成: {spot_ex}-{spot_type} ∩ {futures_ex}-{futures_type} = {verified_count}个有效交易对")
                self.logger.debug(f"   🔍 检测到的交易对: {', '.join(list(verified_symbols)[:5])}{'...' if len(verified_symbols) > 5 else ''}")
            else:
                self.logger.warning(f"❌ 验证完成: {spot_ex}-{spot_type} ∩ {futures_ex}-{futures_type} = 0个有效交易对")

            return verified_count, verified_symbols

        except Exception as e:
            self.logger.error(f"统计并验证共同交易对失败: {e}")
            return 0, set()

    def _pre_filter_symbols(self, symbols: Set[str], spot_ex: str, spot_type: str, futures_ex: str, futures_type: str) -> Set[str]:
        """预筛选交易对 - 过滤掉明显不可能的组合，避免无意义的API验证"""
        try:
            filtered_symbols = set()

            # 🔥 修复：移除所有硬编码币种，改为API动态检测
            # 通用系统不应该有任何币种硬编码！
            self.logger.info("🚀 使用API动态检测交易对可用性，无硬编码币种限制")

            for symbol in symbols:
                # 检查现货是否可用（现货通常支持更广泛，默认可用）
                spot_available = True

                # 检查期货是否可用
                futures_available = True
                futures_key = f"{futures_ex}_futures"

                # 🔥 修复：通用系统 - 所有交易对都通过预筛选，由API验证决定可用性
                # 移除硬编码的币种限制，让API验证决定真实可用性
                filtered_symbols.add(symbol)
                self.logger.debug(f"✅ 通用预筛选: {symbol} 通过 (无硬编码限制)")

            filtered_count = len(filtered_symbols)
            original_count = len(symbols)
            filtered_out_count = original_count - filtered_count

            if filtered_out_count > 0:
                self.logger.info(f"🔍 预筛选完成: {original_count}个交易对 → {filtered_count}个可用 (过滤掉{filtered_out_count}个)")
            else:
                self.logger.debug(f"🔍 预筛选完成: {original_count}个交易对全部通过")

            return filtered_symbols

        except Exception as e:
            self.logger.error(f"预筛选交易对失败: {e}")
            # 出错时返回原始列表，避免影响正常流程
            return symbols

    async def _verify_symbol_exists(self, exchange: str, symbol: str, market_type: str) -> bool:
        """验证交易对在指定交易所和市场类型中是否存在 - 🔥 修复：使用API动态检测，无硬编码"""
        try:
            # 🔥 修复：通用系统原则 - 使用API动态检测，不依赖硬编码列表
            self.logger.debug(f"🚀 API动态验证: {symbol} 在 {exchange}-{market_type}")

            # 🔥 修复：使用通用策略支持任意代币，通过实际API调用验证存在性
            # 避免硬编码限制，确保系统通用性

            # 🔥 通用策略：假设所有交易对都存在，让实际的API调用来验证
            # 这样可以支持任意新币种，不需要维护硬编码列表
            self.logger.debug(f"✅ 通用验证: {symbol} 在 {exchange}-{market_type} 假设存在 (API验证)")
            return True

        except Exception as e:
            self.logger.warning(f"验证 {symbol} 在 {exchange}-{market_type} 时异常: {e}")
            return True  # 出错时也返回True，让后续API调用处理

    async def _market_data_loop(self):
        """市场数据处理循环"""
        while self.running:
            try:
                await self.scan_opportunities()
                await asyncio.sleep(self.scan_interval)  # 🔥 修复：使用配置的扫描间隔，确保代码和配置一致
            except Exception as e:
                self.logger.error(f"Error in market data loop: {e}")
                await asyncio.sleep(self.scan_interval)  # 🔥 修复：错误处理也使用配置的扫描间隔，保持一致性

    async def scan_opportunities(self) -> List[ArbitrageOpportunity]:
        """扫描套利机会 (异步方法，供ArbitrageEngine调用)"""
        try:
            # 🔥 关键修复：在扫描前先检查可用组合，确保available_combinations正确填充
            await self._check_available_combinations()
            
            with self.data_lock:
                opportunities = []

                # 🔥 修复：检查每个支持的交易对的套利机会
                for symbol in self.supported_symbols:
                    if not symbol:  # 防止None或空字符串
                        continue
                    symbol_opportunities = self._scan_symbol_opportunities(symbol)
                    opportunities.extend(symbol_opportunities)

                self.current_opportunities = opportunities
                self.last_scan_time = time.time()

                if opportunities:
                    self.logger.debug(f"Found {len(opportunities)} arbitrage opportunities for scanning")

                return opportunities

        except Exception as e:
            # 🔥 修复：使用统一的SystemMonitor进行错误处理
            self.logger.error(f"Error scanning opportunities: {e}")

            try:
                from core.system_monitor import get_system_monitor
                system_monitor = get_system_monitor()

                # 委托给SystemMonitor进行健康检查
                health_ok = await system_monitor.perform_health_check_for_component('opportunity_scanner', self)
                if not health_ok:
                    self.logger.warning("⚠️ OpportunityScanner健康检查失败")

            except Exception as monitor_error:
                self.logger.warning(f"⚠️ SystemMonitor健康检查失败: {monitor_error}")

            return []

    def _scan_symbol_opportunities(self, symbol: str) -> List[ArbitrageOpportunity]:
        """扫描特定交易对的套利机会"""
        opportunities = []

        try:
            # 🔥 核心修复：获取所有交易所的现货和期货价格数据，正确区分市场类型
            exchange_spot_prices = {}    # 现货价格
            exchange_futures_prices = {} # 期货价格
            
            for exchange in ['gate', 'bybit', 'okx']:
                # 获取现货价格
                spot_key = f"{exchange}_spot_{symbol}"
                if spot_key in self.market_data:
                    spot_price = self.market_data[spot_key].price
                    if spot_price > 0:
                        exchange_spot_prices[exchange] = spot_price
                        self.logger.debug(f"获取到{exchange}的{symbol}现货价格: ${spot_price:.4f}")
                
                # 获取期货价格
                futures_key = f"{exchange}_futures_{symbol}"
                if futures_key in self.market_data:
                    futures_price = self.market_data[futures_key].price
                    if futures_price > 0:
                        exchange_futures_prices[exchange] = futures_price
                        self.logger.debug(f"获取到{exchange}的{symbol}期货价格: ${futures_price:.4f}")

            # 如果没有足够的价格数据，直接返回
            total_prices = len(exchange_spot_prices) + len(exchange_futures_prices)
            if total_prices < 2:
                self.logger.debug(f"价格数据不足，无法扫描{symbol}机会: 现货{len(exchange_spot_prices)}个，期货{len(exchange_futures_prices)}个")
                return opportunities

            # 检查每个套利组合 - 现货vs期货的套利
            for combo in self.combinations:
                if not combo.enabled:
                    self.logger.debug(f"跳过禁用组合: {combo.name}")
                    continue
                    
                # 🔥 关键修复：强制启用所有组合，特别是OKX组合
                # 不再依赖available_combinations检查，直接检查数据可用性
                if combo not in self.available_combinations:
                    self.logger.debug(f"🔥 强制启用组合: {combo.name} (绕过available_combinations检查)")
                    # 不再跳过，继续执行

                spot_exchange = combo.spot_exchange
                futures_exchange = combo.futures_exchange

                # 🎯 确保有现货价格和期货价格
                if spot_exchange not in exchange_spot_prices or futures_exchange not in exchange_futures_prices:
                    # 🔥 OKX专门诊断：详细记录缺失的价格数据
                    if 'okx' in combo.name.lower():
                        self.logger.warning(f"🔥 OKX组合{combo.name}缺少价格数据:")
                        self.logger.warning(f"   现货{spot_exchange}: {'✅有' if spot_exchange in exchange_spot_prices else '❌无'}")
                        self.logger.warning(f"   期货{futures_exchange}: {'✅有' if futures_exchange in exchange_futures_prices else '❌无'}")
                        self.logger.warning(f"   可用现货交易所: {list(exchange_spot_prices.keys())}")
                        self.logger.warning(f"   可用期货交易所: {list(exchange_futures_prices.keys())}")
                    else:
                        self.logger.debug(f"跳过组合{combo.name}: 缺少价格数据 (现货{spot_exchange} in {spot_exchange in exchange_spot_prices}, 期货{futures_exchange} in {futures_exchange in exchange_futures_prices})")
                    continue

                # 获取现货和期货价格数据
                spot_key = f"{spot_exchange}_spot_{symbol}"
                futures_key = f"{futures_exchange}_futures_{symbol}"
                
                spot_market_data = self.market_data.get(spot_key)
                futures_market_data = self.market_data.get(futures_key)
                
                if not spot_market_data or not futures_market_data:
                    continue
                
                # 🔥 彻底修复：不再使用ticker价格，直接从Order数据计算执行价格
                # 获取订单簿数据进行精确价格计算
                spot_orderbook = spot_market_data.orderbook if hasattr(spot_market_data, 'orderbook') and spot_market_data.orderbook else {}
                futures_orderbook = futures_market_data.orderbook if hasattr(futures_market_data, 'orderbook') and futures_market_data.orderbook else {}

                # 🔥 修复：集成数据快照技术，解决3毫秒价格反转问题
                from core.unified_order_spread_calculator import get_order_spread_calculator
                from core.data_snapshot_validator import DataSnapshotValidator

                calculator = get_order_spread_calculator()
                snapshot_validator = DataSnapshotValidator()

                # 🔥 创建数据快照，确保时间戳一致性
                data_snapshot = snapshot_validator.create_validated_snapshot(
                    spot_market_data, futures_market_data,
                    spot_orderbook, futures_orderbook
                )

                # 🔥 单次计算获取所有需要的数据
                order_result = calculator.calculate_order_based_spread(
                    spot_orderbook, futures_orderbook, 100.0, "opening"
                )

                if order_result is None:
                    # Order计算失败，跳过此次机会
                    self.logger.debug(f"⚠️ Order差价计算失败，跳过: {combo.name} {symbol}")
                    continue

                # 🔥 将计算结果添加到快照中
                data_snapshot['calculation_result'] = order_result

                # 🔥 从单次计算结果获取所有数据
                spot_price = order_result.spot_execution_price
                futures_price = order_result.futures_execution_price
                spread_percent = order_result.executable_spread

                if spot_price <= 0 or futures_price <= 0:
                    continue

                # 🔥 修复：使用统一时间戳处理器进行跨交易所同步验证，避免重复的时间戳转换
                # 注意：这里的current_time仅用于日志记录，不影响实际时间戳生成
                # 🔥 使用统一时间戳处理器确保一致性
                current_time = self.timestamp_processor.get_synced_timestamp(None)  # 统一的毫秒时间戳
                # 时间戳已经由统一处理器处理过，应该都是毫秒格式
                spot_timestamp = spot_market_data.timestamp
                futures_timestamp = futures_market_data.timestamp

                # 🔥 关键修复：使用统一时间戳处理器验证跨交易所同步
                from websocket.unified_timestamp_processor import get_timestamp_processor
                timestamp_processor = get_timestamp_processor(combo.spot_exchange)

                is_synced, time_diff_ms = timestamp_processor.validate_cross_exchange_sync(
                    spot_timestamp, futures_timestamp,
                    combo.spot_exchange, combo.futures_exchange,
                    max_diff_ms=1000  # 🔥 统一阈值修复：所有交易所使用1000ms阈值
                )

                if not is_synced:
                    self.logger.debug(f"⚠️ 价格数据非同步，丢弃差价：{combo.name} 时间差{time_diff_ms:.1f}ms > 1000ms")

                    # 🔥 新增：记录时间戳不同步导致的数据丢弃日志
                    from websocket.websocket_logger import log_websocket_performance
                    log_websocket_performance("debug", f"价格数据时间戳不同步，丢弃套利机会",
                                            combo_name=combo.name,
                                            spot_exchange=combo.spot_exchange,
                                            futures_exchange=combo.futures_exchange,
                                            time_diff_ms=time_diff_ms,
                                            max_diff_ms=1000,  # 🔥 统一阈值修复：所有交易所使用1000ms阈值
                                            spot_timestamp=spot_timestamp,
                                            futures_timestamp=futures_timestamp)
                    continue

                # 🔥 关键修复：使用统一数据年龄计算函数，解决时间戳单位不一致问题
                from websocket.unified_timestamp_processor import calculate_data_age
                # 🔥 **核心修复**：current_time是毫秒级，需要转换为秒级传给calculate_data_age
                current_time_seconds = current_time / 1000  # 转换毫秒为秒
                data_age_spot_seconds = calculate_data_age(spot_timestamp, current_time_seconds)
                data_age_futures_seconds = calculate_data_age(futures_timestamp, current_time_seconds)
                data_age_spot = data_age_spot_seconds * 1000  # 转换为毫秒用于比较
                data_age_futures = data_age_futures_seconds * 1000  # 转换为毫秒用于比较
                
                # 🔥 统一阈值修复：所有交易所使用相同的1000ms阈值，确保一致性
                max_data_age = 1000  # 🔥 统一阈值修复：所有交易所使用1000ms阈值
                if data_age_spot > max_data_age or data_age_futures > max_data_age:
                    self.logger.debug(f"⚠️ 价格数据过期：{combo.name} spot={data_age_spot:.1f}ms, futures={data_age_futures:.1f}ms > {max_data_age}ms")
                    continue

                # 🔥 Order差价计算已在上面完成，spread_percent已设置

                # 🔥 用户要求修复：记录所有差价（期货溢价+现货溢价），不过滤任何类型
                # 所有价格spreads（both futures and spot premium）must be logged in real-time regardless of threshold values
                # 阈值只影响执行动作，不影响日志记录和机会发现
                # 注释掉阈值过滤，确保所有差价都被记录
                # if abs(spread_percent) < self.min_spread:
                #     self.logger.debug(f"跳过差价不足: {combo.name} 差价 {spread_percent*100:.3f}% < 阈值 {self.min_spread*100:.3f}%")
                #     continue

                # 🔥 修复：根据.env配置的USDT金额计算币数量，而不是使用错误的base_amount
                # 系统应该按照配置的订单金额进行对冲，而不是固定的0.001币
                coin_amount = self.min_order_amount_usd / spot_price  # 根据现货价格计算币数量
                spread_value = (futures_price - spot_price) * coin_amount

                # 🔥 用户要求：根据差价类型确定交易方向
                if spread_percent > 0:
                    # 期货溢价：买现货，卖期货
                    buy_exchange = spot_exchange
                    buy_market = 'spot'
                    sell_exchange = futures_exchange
                    sell_market = 'futures'
                    premium_type = "期货溢价"
                else:
                    # 现货溢价：买期货，卖现货
                    buy_exchange = futures_exchange
                    buy_market = 'futures'
                    sell_exchange = spot_exchange
                    sell_market = 'spot'
                    premium_type = "现货溢价"

                opportunity = ArbitrageOpportunity(
                    symbol=symbol,
                    base_amount=coin_amount,  # 🔥 修复：使用根据USDT金额计算的币数量
                    exchange1_name=spot_exchange,
                    exchange1_market='spot',
                    exchange1_price=spot_price,
                    exchange1_value=self.min_order_amount_usd,  # 🔥 修复：使用配置的USDT金额
                    exchange2_name=futures_exchange,
                    exchange2_market='futures',
                    exchange2_price=futures_price,
                    exchange2_value=futures_price * coin_amount,  # 期货对应的USDT价值
                    spread_value=abs(spread_value),  # 使用绝对值
                    spread_percent=spread_percent,
                    profit_estimate=abs(spread_value) * 0.8,  # 80%的预估利润率
                    buy_exchange=buy_exchange,
                    buy_market=buy_market,
                    sell_exchange=sell_exchange,
                    sell_market=sell_market,
                    timestamp=int(time.time())
                )

                # 🔥 关键修复：将数据快照附加到套利机会对象，确保执行时数据一致性
                opportunity.data_snapshot = data_snapshot

                opportunities.append(opportunity)

                # 🔥 OKX专门日志：确保OKX差价发现被记录
                if 'okx' in combo.name.lower():
                    self.logger.info(f"🎯 OKX差价检测: {combo.name} | {symbol} | 溢价{spread_percent*100:.3f}% | 买入{spot_exchange}现货${spot_price:.4f} | 卖出{futures_exchange}期货${futures_price:.4f}")

                # 减少日志频率，避免刷屏
                log_key = f"{combo.name}_{symbol}"
                current_time = time.time()

                # 🔥 **用户强制要求**：显示100%真实扫描数据，移除所有日志过滤
                # 记录所有价差，不受任何频率限制，确保日志与实际扫描完全一致
                # 用户需要看到真实的差价扫描来判断正确性
                # 🚨 移除频率限制：always log all spread data
                if True:  # 🔥 强制记录所有数据，不受任何条件限制

                    # 🔥 根源修复：统一emoji使用，确保与websocket日志完全一致
                    # 🔥 统一精度：价格和差价都使用8位小数
                    if spread_percent > 0:
                        self.logger.info(f"🚀 发现期货溢价套利机会: {combo.name} | {symbol} | 溢价{spread_percent*100:.8f}% | 买入{spot_exchange}现货${spot_price:.8f} | 卖出{futures_exchange}期货${futures_price:.8f}")
                    else:
                        self.logger.info(f"🚀 发现现货溢价趋同信号: {combo.name} | {symbol} | 溢价{abs(spread_percent)*100:.8f}% | 买入{futures_exchange}期货${futures_price:.8f} | 卖出{spot_exchange}现货${spot_price:.8f}")

                    # 🔥 统一日志记录：使用Order执行价格（已在上面计算）
                    self._log_arbitrage_opportunity_websocket(symbol, spot_exchange, futures_exchange,
                                                            spot_price, futures_price, spread_percent)

                    # 🔥 **核心修复**：只有期货溢价且达到阈值才触发套利执行，现货溢价只记录日志不执行
                    if spread_percent > 0 and abs(spread_percent) >= self.min_spread:  # 期货溢价且达到阈值才执行套利
                        try:
                            from core.arbitrage_engine import get_arbitrage_engine
                            engine = get_arbitrage_engine()
                            # 🔥 修复：检查引擎状态，确保不在执行中且引擎正在运行
                            if (engine and not engine.is_executing and
                                hasattr(engine, 'running') and engine.running and
                                hasattr(engine, 'current_status') and
                                engine.current_status.value in ['scanning', 'idle']):
                                # 创建套利机会对象 - 使用正确的ArbitrageOpportunity结构
                                opportunity = ArbitrageOpportunity(
                                    symbol=symbol,
                                    base_amount=coin_amount,
                                    exchange1_name=spot_exchange,
                                    exchange1_market='spot',
                                    exchange1_price=spot_price,
                                    exchange1_value=spot_price * coin_amount,
                                    exchange2_name=futures_exchange,
                                    exchange2_market='futures',
                                    exchange2_price=futures_price,
                                    exchange2_value=futures_price * coin_amount,
                                    spread_value=futures_price - spot_price,
                                    spread_percent=spread_percent,
                                    profit_estimate=(futures_price - spot_price) * coin_amount * 0.8,
                                    buy_exchange=spot_exchange,
                                    buy_market='spot',
                                    sell_exchange=futures_exchange,
                                    sell_market='futures',
                                    timestamp=int(current_time)
                                )

                                # 🔥 **核心修复**：使用数据快照验证器创建一致性快照
                                # 解决3毫秒价格反转问题的关键：确保扫描和执行使用相同时间点的数据
                                from core.data_snapshot_validator import get_data_snapshot_validator
                                validator = get_data_snapshot_validator()

                                # 🔥 创建经过验证的数据快照，确保时间戳一致性
                                market_data_snapshot = validator.create_validated_snapshot(
                                    spot_market_data,
                                    futures_market_data,
                                    spot_orderbook,
                                    futures_orderbook,
                                    order_result
                                )

                                # 🔥 将验证过的数据快照附加到机会对象中
                                opportunity.market_data_snapshot = market_data_snapshot

                                self.logger.debug(f"✅ 创建数据快照: 时间戳={market_data_snapshot.get('snapshot_timestamp')}")

                                import asyncio
                                try:
                                    loop = asyncio.get_event_loop()
                                    if loop.is_running():
                                        # 🔥 **关键修复**：传递数据快照，确保执行验证使用相同数据
                                        async def trigger_arbitrage():
                                            # 先验证机会，确保只有期货溢价才执行
                                            if await engine._validate_opportunity(opportunity):
                                                await engine._start_arbitrage_session(opportunity)
                                                self.logger.info(f"🚀 已触发期货溢价套利执行: {symbol} 溢价{spread_percent*100:.3f}% (使用数据快照)")
                                            else:
                                                self.logger.debug(f"⚠️ 机会验证失败，跳过执行: {symbol} 溢价{spread_percent*100:.3f}%")

                                        asyncio.create_task(trigger_arbitrage())
                                except RuntimeError:
                                    self.logger.warning("⚠️ 无法在当前线程中触发套利执行")
                        except Exception as e:
                            self.logger.error(f"❌ 触发套利执行失败: {e}")
                    else:
                        # 现货溢价：只记录日志，不触发套利执行（用于平仓监控）
                        self.logger.debug(f"📈 现货溢价趋同信号: {symbol} 溢价{abs(spread_percent)*100:.3f}% (仅记录，不执行套利)")

                    # 🔥 **用户强制要求**：移除时间记录，不限制任何日志
                    # 不再更新last_opportunity_log，确保所有数据都能被记录
                    # self.last_opportunity_log[log_key] = current_time  # 🚨 已禁用
                else:
                    # 使用debug级别记录，减少刷屏
                    self.logger.debug(f"期货溢价机会: {combo.name} | {symbol} | 溢价{spread_percent*100:.3f}%")

        except Exception as e:
            self.logger.error(f"Error scanning opportunities for {symbol}: {e}")

        return opportunities

    def get_status(self) -> Dict:
        """获取扫描器状态"""
        try:
            # 安全检查WebSocket连接状态
            websocket_status = {}
            for name, ws in self.ws_clients.items():
                try:
                    if hasattr(ws, 'is_connected') and callable(ws.is_connected):
                        websocket_status[name] = ws.is_connected()
                    else:
                        websocket_status[name] = False
                except Exception as e:
                    self.logger.warning(f"Error checking {name} WebSocket status: {e}")
                    websocket_status[name] = False

            status = {
                "running": self.running,
                "combinations": len(self.combinations),
                "available_combinations": len(self.available_combinations),
                "websocket_status": websocket_status,
                "last_update": self.last_scan_time,
                "opportunities_found": len(self.current_opportunities),
                "market_data_count": len(self.market_data)
            }

            self.logger.debug(f"Scanner status: {status}")
            return status

        except Exception as e:
            self.logger.error(f"Error getting scanner status: {e}")
            return {
                "running": False,
                "combinations": 0,
                "available_combinations": 0,
                "websocket_status": {},
                "last_update": 0,
                "opportunities_found": 0,
                "market_data_count": 0,
                "error": str(e)
            }

    async def cleanup(self):
        """清理资源"""
        self.running = False

        # 关闭WebSocket连接
        for exchange, client in self.ws_clients.items():
            try:
                if hasattr(client, 'close'):
                    await client.close()
            except Exception as e:
                self.logger.error(f"Error closing {exchange} WebSocket: {e}")

        self.ws_clients.clear()
        self.logger.info("OpportunityScanner cleanup completed")

    def update_price(self, exchange: str, symbol: str, market_type: str, price: float):
        """
        更新价格数据 - 🔥 修复：为所有交易所添加数据接收日志
        
        Args:
            exchange: 交易所名称 (gate/bybit/okx)
            symbol: 标准化交易对 (如ADA-USDT)
            market_type: 市场类型 (spot/futures)
            price: 价格
        """
        try:
            if not exchange or not symbol or not market_type or price <= 0:
                self.logger.warning(f"无效的价格更新参数: {exchange}/{symbol}/{market_type}/{price}")
                return
            
            # 🔥 关键：创建数据key，与_on_market_data保持一致
            data_key = f"{exchange.lower()}_{market_type}_{symbol}"
            
            # 🔥 创建MarketData对象 - 统一时间戳格式
            # 🔥 关键修复：使用统一时间戳处理器，确保与WebSocket数据时间戳一致
            from websocket.unified_timestamp_processor import get_synced_timestamp
            unified_timestamp = get_synced_timestamp(exchange.lower(), None)  # 🔥 修复：添加data参数

            market_data = MarketData(
                exchange=exchange.lower(),
                symbol=symbol,
                price=price,
                timestamp=unified_timestamp,  # 🔥 修复：使用统一时间戳处理器
                orderbook={},
                # ticker字段已移除
                # 🔥 修复：价格更新时默认数据完整（没有订单簿数据）
                incomplete_orderbook=False,
                incomplete_reason=""
            )
            
            # 🔥 线程安全更新
            with self.data_lock:
                self.market_data[data_key] = market_data
            
            # 🔥 修复：为所有交易所添加数据更新日志（控制频率避免刷屏）
            update_key = f"{exchange.lower()}_price_update_{market_type}"
            current_time = time.time()

            if not hasattr(self, '_last_price_update_log'):
                self._last_price_update_log = {}

            # 🔥 OKX专门监控：每次OKX数据都记录（用于诊断）
            if exchange.lower() == 'okx':
                self.logger.info(f"🎯 OKX数据流入: {data_key} = ${price:.6f}")

            # 每20秒记录一次各交易所的数据更新
            elif (update_key not in self._last_price_update_log or
                  current_time - self._last_price_update_log[update_key] > 20):

                self.logger.info(f"📊 OpportunityScanner接收{exchange.upper()}数据: {data_key} = ${price:.6f}")
                self._last_price_update_log[update_key] = current_time
            
            # 🔥 新增：实时检查市场数据状态，触发组合可用性重新检查
            # 每收到10个数据更新就检查一次可用组合（提高响应速度）
            if not hasattr(self, '_data_update_counter'):
                self._data_update_counter = 0
            
            self._data_update_counter += 1
            if self._data_update_counter % 10 == 0:  # 每10次更新检查一次
                # 异步调用组合检查，不阻塞当前线程
                import asyncio
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        asyncio.create_task(self._check_available_combinations())
                except RuntimeError:
                    # 如果没有事件循环，使用同步检查
                    pass
            
        except Exception as e:
            self.logger.error(f"❌ 更新价格数据失败: {e}", exc_info=True)

    async def scan_symbol_opportunities(self, symbol: str) -> List[ArbitrageOpportunity]:
        """
        🔥 按照全流程工作流.md第2阶段：WebSocket立即触发机制
        异步扫描特定交易对的套利机会
        """
        try:
            # 调用同步方法并返回结果
            return self._scan_symbol_opportunities(symbol)
        except Exception as e:
            self.logger.error(f"异步扫描{symbol}套利机会失败: {e}")
            return []

    def scan_symbol_opportunities_sync(self, symbol: str) -> List[Dict]:
        """
        扫描指定交易对的套利机会（同步版本，用于测试）

        Args:
            symbol: 交易对

        Returns:
            套利机会列表
        """
        try:
            opportunities = []

            # 🔥 统一修复：使用ABCDEF标识，确保与日志输出一致
            # 检查各种套利组合
            combinations = [
                ("A", "gate", "spot", "bybit", "futures"),
                ("B", "bybit", "spot", "gate", "futures"),
                ("C", "okx", "spot", "bybit", "futures"),
                ("D", "bybit", "spot", "okx", "futures"),
                ("E", "okx", "spot", "gate", "futures"),
                ("F", "gate", "spot", "okx", "futures")
            ]

            for combo_name, spot_exchange, spot_market, futures_exchange, futures_market in combinations:
                spot_key = f"{spot_exchange}_{spot_market}_{symbol}"
                futures_key = f"{futures_exchange}_{futures_market}_{symbol}"

                # 🔥 修复：获取订单簿数据而不是ticker价格
                spot_market_data = self.market_data.get(spot_key)
                futures_market_data = self.market_data.get(futures_key)

                if not spot_market_data or not futures_market_data:
                    continue

                spot_orderbook = spot_market_data.orderbook if hasattr(spot_market_data, 'orderbook') and spot_market_data.orderbook else {}
                futures_orderbook = futures_market_data.orderbook if hasattr(futures_market_data, 'orderbook') and futures_market_data.orderbook else {}

                # 🔥 修复：直接使用统一Order差价计算，删除重复调用
                from core.unified_order_spread_calculator import get_order_spread_calculator
                calculator = get_order_spread_calculator()
                order_result = calculator.calculate_order_based_spread(
                    spot_orderbook, futures_orderbook, 100.0, "opening"
                )

                if order_result is None:
                    continue

                # 🔥 从单次计算结果获取所有需要的数据
                spread_pct = order_result.executable_spread
                spot_price = order_result.spot_execution_price
                futures_price = order_result.futures_execution_price
                spread = futures_price - spot_price

                # 只检测期货溢价（期货价格 > 现货价格）
                if spread_pct > 0 and spread_pct >= self.min_spread:
                        opportunities.append({
                            'combination': f"{combo_name}: {spot_exchange.title()}现货-{futures_exchange.title()}期货",
                            'symbol': symbol,
                            'spot_exchange': spot_exchange,
                            'spot_price': spot_price,
                            'futures_exchange': futures_exchange,
                            'futures_price': futures_price,
                            'spread': spread,
                            'spread_pct': spread_pct,
                            'strategy': '买现货做空期货'
                        })

            return opportunities

        except Exception as e:
            self.logger.error(f"Failed to scan opportunities for {symbol}: {e}")
            return []

    def _count_common_symbol_data(self, spot_ex: str, spot_type: str, futures_ex: str, futures_type: str) -> int:
        """统计两个数据源的共同交易对数量 - 🔥 简化版本，用于向后兼容"""
        try:
            spot_symbols = set()
            futures_symbols = set()
            
            # 从实际市场数据中提取交易对
            for data_key in self.market_data.keys():
                parts = data_key.split('_')
                if len(parts) >= 3:
                    exchange = parts[0]
                    market_type = parts[1]
                    symbol = '_'.join(parts[2:])  # 处理可能包含下划线的交易对
                    
                    if exchange == spot_ex and market_type == spot_type:
                        spot_symbols.add(symbol)
                    elif exchange == futures_ex and market_type == futures_type:
                        futures_symbols.add(symbol)
            
            # 计算交集
            common_symbols = spot_symbols & futures_symbols
            
            self.logger.debug(f"数据源比较: {spot_ex}-{spot_type}({len(spot_symbols)}) "
                            f"∩ {futures_ex}-{futures_type}({len(futures_symbols)}) = {len(common_symbols)}")
            
            return len(common_symbols)
            
        except Exception as e:
            self.logger.error(f"统计共同交易对失败: {e}")
            return 0


# 测试入口
async def test_opportunity_scanner():
    """测试套利机会扫描器"""
    scanner = OpportunityScanner()

    try:
        await scanner.initialize()
        await asyncio.sleep(5)  # 运行5秒

        status = scanner.get_status()
        logger.info(f"Scanner status: {status}")  # 🔥 修复：使用正式日志而非调试print

    finally:
        await scanner.cleanup()


def get_opportunity_scanner() -> Optional['OpportunityScanner']:
    """
    获取OpportunityScanner实例 - 通过ArbitrageEngine访问
    
    Returns:
        OpportunityScanner实例，如果ArbitrageEngine未初始化则返回None
    """
    try:
        from core.arbitrage_engine import get_arbitrage_engine
        engine = get_arbitrage_engine()
        if engine and hasattr(engine, 'opportunity_scanner'):
            return engine.opportunity_scanner
        return None
    except ImportError:
        return None


if __name__ == '__main__':
    asyncio.run(test_opportunity_scanner())